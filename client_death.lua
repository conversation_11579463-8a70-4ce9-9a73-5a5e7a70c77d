-- =====================================================
-- KOTH DEATH SYSTEM - Clean Implementation
-- =====================================================
print('[KOTH DEATH] Loading death system...')

-- Death system variables
local DeathSystem = {
    isDead = false,
    deathTime = 0,
    killerServerId = nil,
    respawnHoldTime = 0,
    respawnProgress = 0,
    hasReleasedE = true,
    bleedoutDuration = 50000, -- 50 seconds in milliseconds
    respawnHoldDuration = 5000, -- 5 seconds to respawn
    lastDamageSource = nil, -- Track who last damaged us
    lastDamageTime = 0, -- Track when we were last damaged

    -- Internal flag to indicate that the dead body has been frozen
    -- on the ground.  When false the death system allows the
    -- ragdoll to fall naturally.  Once the ped is detected to be
    -- resting on the ground the body will be frozen and this flag
    -- flipped to true.  See the main death loop for details.
    hasFrozenOnGround = false,

    -- Team spawn coordinates
    teamSpawns = {
        -- Default spawns; these will be overridden by the koth:updateTeamSpawns
        -- event when the server sends new spawn data on map rotation.  See
        -- RegisterNetEvent('koth:updateTeamSpawns') below.
        red   = { x = 0.0, y = 0.0, z = 0.0, heading = 0.0 },
        blue  = { x = 0.0, y = 0.0, z = 0.0, heading = 0.0 },
        green = { x = 0.0, y = 0.0, z = 0.0, heading = 0.0 }
    },

    -- KOTH zone for kill detection
    kothZone = {
        x = 2842.4216,
        y = 2864.8088,
        z = 62.5975,
        -- Increase the KOTH radius from 150 to 300 for kill detection
        radius = 300.0
    }
}

-- =====================================================
-- DAMAGE TRACKING SYSTEM
-- =====================================================
Citizen.CreateThread(function()
    while true do
        local playerPed = PlayerPedId()

        -- Only track damage when alive
        if not DeathSystem.isDead then
            -- Check all players for who might have damaged us
            for _, playerId in ipairs(GetActivePlayers()) do
                local otherPed = GetPlayerPed(playerId)

                -- Skip self
                if otherPed ~= playerPed then
                    -- Check if this player recently damaged us
                    if HasEntityBeenDamagedByEntity(playerPed, otherPed, true) then
                        DeathSystem.lastDamageSource = GetPlayerServerId(playerId)
                        DeathSystem.lastDamageTime = GetGameTimer()

                        local damagerName = GetPlayerName(playerId) or 'Unknown'
                        print('[KOTH DEATH] Damage detected from player:', damagerName, 'Server ID:', DeathSystem.lastDamageSource)

                        -- Clear the damage flag
                        ClearEntityLastDamageEntity(playerPed)
                    end
                end
            end
        end

        Citizen.Wait(100) -- Check every 100ms
    end
end)

-- =====================================================
-- DEATH ANIMATION SYSTEM
-- =====================================================
local function playDeathAnimation()
    local playerPed = PlayerPedId()

    -- Request animation dictionary
    RequestAnimDict("dead")
    while not HasAnimDictLoaded("dead") do
        Citizen.Wait(1)
    end

    -- Play death animation
    TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)

    -- Set the player into a prolonged ragdoll state.  Use a long duration
    -- so the body can fall naturally to the ground before being frozen.
    -- The first two arguments specify how long the ragdoll lasts; using
    -- 10000ms gives plenty of time for the ped to settle on the ground.
    SetPedToRagdoll(playerPed, 10000, 10000, 0, true, true, false)

    print('[KOTH DEATH] Death animation applied')
end

-- =====================================================
-- KILLER DETECTION SYSTEM - ENHANCED VERSION
-- =====================================================
local function getKillerInfo(playerPed)
    print('[KOTH DEATH] Getting killer info...')

    -- Method 1: Use our damage tracking system first (most reliable)
    local killerServerId = nil

    -- Check if we have recent damage within last 5 seconds
    if DeathSystem.lastDamageSource and (GetGameTimer() - DeathSystem.lastDamageTime) < 5000 then
        killerServerId = DeathSystem.lastDamageSource
        print('[KOTH DEATH] Using damage tracker - Killer Server ID:', killerServerId)

        -- Clear the damage tracking
        DeathSystem.lastDamageSource = nil
        DeathSystem.lastDamageTime = 0

        return killerServerId
    end

    -- Method 2: GetPedSourceOfDeath
    local killerPed = GetPedSourceOfDeath(playerPed)
    print('[KOTH DEATH] GetPedSourceOfDeath returned:', killerPed)

    if killerPed and killerPed ~= 0 and killerPed ~= playerPed then
        if IsPedAPlayer(killerPed) then
            local killerPlayerIndex = NetworkGetPlayerIndexFromPed(killerPed)
            print('[KOTH DEATH] Killer player index:', killerPlayerIndex)

            if killerPlayerIndex and killerPlayerIndex ~= -1 then
                killerServerId = GetPlayerServerId(killerPlayerIndex)
                print('[KOTH DEATH] Killer server ID from ped:', killerServerId)
            end
        end
    end

    -- Method 3: GetPedKiller (backup method)
    if not killerServerId or killerServerId == 0 then
        local killerPed2 = GetPedKiller(playerPed)
        print('[KOTH DEATH] GetPedKiller returned:', killerPed2)

        if killerPed2 and killerPed2 ~= 0 and killerPed2 ~= playerPed then
            if IsPedAPlayer(killerPed2) then
                local killerPlayerIndex2 = NetworkGetPlayerIndexFromPed(killerPed2)
                if killerPlayerIndex2 and killerPlayerIndex2 ~= -1 then
                    killerServerId = GetPlayerServerId(killerPlayerIndex2)
                    print('[KOTH DEATH] Killer server ID from GetPedKiller:', killerServerId)
                end
            end
        end
    end

    -- Method 4: Check recent damage events (most reliable)
    if not killerServerId or killerServerId == 0 then
        print('[KOTH DEATH] Checking recent damage events...')

        -- Check all players
        for _, playerId in ipairs(GetActivePlayers()) do
            local otherPed = GetPlayerPed(playerId)

            -- Skip self
            if otherPed ~= playerPed then
                -- Check if this player recently damaged us
                if HasEntityBeenDamagedByEntity(playerPed, otherPed, true) then
                    killerServerId = GetPlayerServerId(playerId)
                    print('[KOTH DEATH] Found killer via damage check - Server ID:', killerServerId)
                    break
                end
            end
        end
    end

    -- Method 5: Check who killed us with specific weapon
    if not killerServerId or killerServerId == 0 then
        local causeOfDeath = GetPedCauseOfDeath(playerPed)
        print('[KOTH DEATH] Cause of death hash:', causeOfDeath)

        if causeOfDeath and causeOfDeath ~= 0 then
            -- Check all players for who has this weapon out
            for _, playerId in ipairs(GetActivePlayers()) do
                local otherPed = GetPlayerPed(playerId)

                if otherPed ~= playerPed then
                    local _, weaponHash = GetCurrentPedWeapon(otherPed, true)

                    -- Check if they have the weapon that killed us and are nearby
                    if weaponHash == causeOfDeath then
                        local distance = #(GetEntityCoords(playerPed) - GetEntityCoords(otherPed))

                        -- Within reasonable kill distance (adjust as needed)
                        if distance < 300.0 then
                            killerServerId = GetPlayerServerId(playerId)
                            print('[KOTH DEATH] Found potential killer with matching weapon - Server ID:', killerServerId)
                            break
                        end
                    end
                end
            end
        end
    end

    print('[KOTH DEATH] Final killer server ID:', killerServerId or 'nil')
    return killerServerId
end

-- =====================================================
-- ZONE DETECTION SYSTEM
-- =====================================================
local function isPlayerInKothZone(playerCoords)
    local distance = #(playerCoords - vector3(DeathSystem.kothZone.x, DeathSystem.kothZone.y, DeathSystem.kothZone.z))
    return distance <= DeathSystem.kothZone.radius
end
-- Keep the death-system zone synced with the server's active map
RegisterNetEvent('koth:rotateMap')
AddEventHandler('koth:rotateMap', function(data)
    if data and data.zone then
        DeathSystem.kothZone.x = data.zone.x or DeathSystem.kothZone.x
        DeathSystem.kothZone.y = data.zone.y or DeathSystem.kothZone.y
        DeathSystem.kothZone.z = data.zone.z or DeathSystem.kothZone.z
        DeathSystem.kothZone.radius = data.zone.radius or DeathSystem.kothZone.radius
        print(string.format('[KOTH DEATH] Zone updated from server: (%.1f, %.1f, %.1f) r=%.1f',
            DeathSystem.kothZone.x, DeathSystem.kothZone.y, DeathSystem.kothZone.z, DeathSystem.kothZone.radius))
    end
end)


-- =====================================================
-- RESPAWN SYSTEM
-- =====================================================
local function respawnPlayer()
    local playerPed = PlayerPedId()
    local playerTeam = GetResourceKvpString('playerTeam') or 'red'
    local spawn = DeathSystem.teamSpawns[playerTeam] or DeathSystem.teamSpawns.red

    print('[KOTH DEATH] Respawning player at team base:', playerTeam)

    -- Respawn player at team base
    NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)

    -- Remove invincibility on respawn.  The flag is set when the
    -- player dies to protect them during the death screen.  Clearing
    -- it ensures the player can take damage again after respawning.
    SetEntityInvincible(playerPed, false)

    -- Clear death state
    DeathSystem.isDead = false
    DeathSystem.respawnHoldTime = 0
    DeathSystem.respawnProgress = 0
    DeathSystem.hasReleasedE = true

    -- Clear tasks and unfreeze
    ClearPedTasksImmediately(playerPed)
    FreezeEntityPosition(playerPed, false)

    -- Disable ragdoll and collision damage after respawn to prevent
    -- griefing by vehicles while still allowing world collisions.
    SetPedCanRagdoll(playerPed, false)
    SetEntityProofs(playerPed, false, false, false, true, false, false, false, false)

    -- Reapply team appearance after respawn
    Citizen.SetTimeout(500, function()
        -- Trigger the team appearance event
        TriggerEvent('koth:applyTeamAppearance', playerTeam)
        print('[KOTH DEATH] Team appearance reapplied after respawn')
    end)

    -- Give basic weapon
    GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)

    -- Hide death screen
    SendNUIMessage({ action = 'hideDeathScreen' })

    -- Show respawn notification
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("Respawned at team base")
    EndTextCommandThefeedPostTicker(false, true)

    print('[KOTH DEATH] Player respawned successfully')

    -- Notify the server that we have respawned (no longer downed).  This
    -- mirrors the notification sent when revived by a medic.  Without
    -- this event medics might continue to see a revive prompt on our
    -- downed body after we have chosen to respawn.
    local myServerId = GetPlayerServerId(PlayerId())
    TriggerServerEvent('koth:playerRevived', myServerId)
end

-- Update DeathSystem team spawns when the server rotates maps.  This
-- allows the death system to respawn players at the correct team base
-- after they die.  The server sends new spawn coordinates via the
-- koth:updateTeamSpawns event (triggered in client.lua when map rotates).
RegisterNetEvent('koth:updateTeamSpawns', function(spawns)
    if spawns and type(spawns) == 'table' then
        DeathSystem.teamSpawns = spawns
        print('[KOTH DEATH] Updated team spawns for death system')
    end
end)

-- =====================================================
-- DEATH SCREEN UI SYSTEM
-- =====================================================
local function showDeathScreen(killerServerId)
    local killerName = 'Unknown'
    local killerId = 0

    if killerServerId and killerServerId ~= 0 then
        -- Try multiple methods to get the killer's name
        local killerPlayerIndex = GetPlayerFromServerId(killerServerId)

        if killerPlayerIndex and killerPlayerIndex ~= -1 then
            killerName = GetPlayerName(killerPlayerIndex)

            -- If name is still not found, try alternative method
            if not killerName or killerName == '' then
                -- Loop through all players to find matching server ID
                for _, playerId in ipairs(GetActivePlayers()) do
                    if GetPlayerServerId(playerId) == killerServerId then
                        killerName = GetPlayerName(playerId) or 'Unknown'
                        break
                    end
                end
            end
        end

        killerId = killerServerId
        print('[KOTH DEATH] Killer found - Name:', killerName, 'Server ID:', killerId)
    else
        print('[KOTH DEATH] No valid killer server ID provided')
    end

    -- The NUI expects a single object with id and name fields for the killer.
    -- Sending separate killer and killerId fields resulted in the UI treating
    -- the killer as a plain string and defaulting to 'Unknown'.  Wrap the
    -- data into an object so that script.js can read killer.id and killer.name.
    SendNUIMessage({
        action = 'showDeathScreen',
        killer = { id = killerId, name = killerName }
    })

    print('[KOTH DEATH] Death screen shown for killer:', killerName, 'ID:', killerId)
end

local function updateDeathTimer()
    local elapsedTime = (GetGameTimer() - DeathSystem.deathTime) / 1000
    local bleedoutRemaining = math.max(0, 50 - math.floor(elapsedTime))

    SendNUIMessage({
        action = 'updateDeathTimer',
        bleedoutTimer = bleedoutRemaining
    })
end

local function updateRespawnProgress()
    SendNUIMessage({
        action = 'updateRespawnProgress',
        progress = DeathSystem.respawnProgress
    })
end

-- =====================================================
-- MAIN DEATH DETECTION THREAD
-- =====================================================
Citizen.CreateThread(function()
    while true do
        local playerPed = PlayerPedId()

        -- Check if player just died
        if IsEntityDead(playerPed) and not DeathSystem.isDead then
            print('[KOTH DEATH] Player death detected')

            -- Set death state
            DeathSystem.isDead = true
            DeathSystem.deathTime = GetGameTimer()
            DeathSystem.respawnHoldTime = 0
            DeathSystem.respawnProgress = 0
            DeathSystem.hasReleasedE = false

            -- Disable auto-respawn
            NetworkResurrectLocalPlayer(GetEntityCoords(playerPed), GetEntityHeading(playerPed), false, false)

            -- Do not immediately freeze the player on death.  Allow
            -- the ragdoll to fall naturally to the ground.  Reset
            -- our freeze flag so the body will be frozen once
            -- settled.
            DeathSystem.hasFrozenOnGround = false
            playDeathAnimation()

            -- Make the player invincible while dead. Without this,
            -- players can continue taking damage (for example, being
            -- executed multiple times) while the death screen is
            -- displayed. Setting invincibility prevents further
            -- damage until the player respawns.
            SetEntityInvincible(playerPed, true)

            -- Notify the server that this player is downed.  The server
            -- will broadcast to all other clients that this player can be
            -- revived by a medic.  We use the player's server ID here.
            local myServerId = GetPlayerServerId(PlayerId())
            TriggerServerEvent('koth:playerDowned', myServerId)

            -- Get killer information
            DeathSystem.killerServerId = getKillerInfo(playerPed)

            -- Check if death was in KOTH zone
            local playerCoords = GetEntityCoords(playerPed)
            local inKothZone = isPlayerInKothZone(playerCoords)

            -- Report kill to server with extensive debugging
            local myServerId = GetPlayerServerId(PlayerId())
            local myPlayerName = GetPlayerName(PlayerId())
            local killerName = 'Unknown'

            if DeathSystem.killerServerId and DeathSystem.killerServerId ~= 0 then
                killerName = GetPlayerName(GetPlayerFromServerId(DeathSystem.killerServerId)) or 'Unknown'

                print('=== [KOTH DEATH] KILL EVENT DEBUG ===')
                print('[KOTH DEATH] My Server ID:', myServerId)
                print('[KOTH DEATH] My Player Name:', myPlayerName)
                print('[KOTH DEATH] Killer Server ID:', DeathSystem.killerServerId)
                print('[KOTH DEATH] Killer Player Name:', killerName)
                print('[KOTH DEATH] Death in KOTH Zone:', inKothZone)
                print('[KOTH DEATH] My Coords:', GetEntityCoords(playerPed))
                print('[KOTH DEATH] KOTH Zone Coords:', DeathSystem.kothZone.x, DeathSystem.kothZone.y, DeathSystem.kothZone.z)
                print('[KOTH DEATH] Distance to Zone:', #(GetEntityCoords(playerPed) - vector3(DeathSystem.kothZone.x, DeathSystem.kothZone.y, DeathSystem.kothZone.z)))
                print('[KOTH DEATH] Zone Radius:', DeathSystem.kothZone.radius)
                print('=== [KOTH DEATH] SENDING TO SERVER ===')

                TriggerServerEvent('koth:playerKilled', DeathSystem.killerServerId, myServerId, inKothZone)

                print('[KOTH DEATH] Kill event sent to server successfully')
            else
                print('=== [KOTH DEATH] KILL EVENT FAILED ===')
                print('[KOTH DEATH] Killer Server ID invalid:', DeathSystem.killerServerId)
                print('[KOTH DEATH] My Server ID:', myServerId)
                print('[KOTH DEATH] Not reporting kill to server')
            end

            -- Show death screen
            showDeathScreen(DeathSystem.killerServerId)

        elseif DeathSystem.isDead then
            -- Player is dead, handle death state
            local playerPed = PlayerPedId()

            -- Maintain death animation
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                playDeathAnimation()
            end

            -- Freeze the dead body only after it has come to rest on
            -- the ground.  Prior to that, keep collisions enabled so
            -- the ragdoll can fall naturally.  Once the ped is no
            -- longer in the air and its height above ground is very
            -- small, we freeze it in place exactly once.  If the
            -- freeze flag has already been set then continue
            -- freezing each frame.
            if not DeathSystem.hasFrozenOnGround then
                local inAir = IsEntityInAir(playerPed)
                -- Use GetEntityHeightAboveGround to determine when the
                -- ped is essentially on the ground.  A threshold of
                -- 0.5 units prevents freezing while the body still
                -- descends down slopes.
                local height = GetEntityHeightAboveGround(playerPed)
                if not inAir and height < 0.5 then
                    FreezeEntityPosition(playerPed, true)
                    DeathSystem.hasFrozenOnGround = true
                else
                    -- Keep the body unfrozen so it can fall
                    FreezeEntityPosition(playerPed, false)
                end
            else
                -- Already frozen; maintain the frozen state
                FreezeEntityPosition(playerPed, true)
            end

            -- Update death timer
            updateDeathTimer()

            -- Handle respawn input (E key)
            if IsControlPressed(0, 38) then -- E key
                if DeathSystem.hasReleasedE then
                    -- Increment hold time based on frame time
                    local frameTime = GetFrameTime()
                    DeathSystem.respawnHoldTime = DeathSystem.respawnHoldTime + (frameTime * 1000) -- Convert to milliseconds

                    -- Calculate progress (0-100%)
                    DeathSystem.respawnProgress = math.min(100, (DeathSystem.respawnHoldTime / DeathSystem.respawnHoldDuration) * 100)

                    -- Update progress bar
                    updateRespawnProgress()

                    -- Respawn when held for required duration
                    if DeathSystem.respawnHoldTime >= DeathSystem.respawnHoldDuration then
                        respawnPlayer()
                    end
                end
            else
                -- E key released
                DeathSystem.hasReleasedE = true
                if DeathSystem.respawnHoldTime > 0 then
                    DeathSystem.respawnHoldTime = 0
                    DeathSystem.respawnProgress = 0
                    updateRespawnProgress()
                end
            end

            -- Auto-respawn after bleedout
            local elapsedTime = GetGameTimer() - DeathSystem.deathTime
            if elapsedTime >= DeathSystem.bleedoutDuration then
                print('[KOTH DEATH] Auto-respawning after bleedout')
                respawnPlayer()
            end

        elseif not IsEntityDead(playerPed) and not DeathSystem.isDead then
            -- Player is alive, ensure they're not stuck in injured state
            if IsPedFatallyInjured(playerPed) then
                ClearPedTasksImmediately(playerPed)
            end
        end

        Citizen.Wait(0)
    end
end)

-- =====================================================
-- INPUT CONTROL SYSTEM
-- =====================================================
Citizen.CreateThread(function()
    while true do
        if DeathSystem.isDead then
            -- Disable all controls except E key
            DisableControlAction(0, 7, true)   -- L
            DisableControlAction(0, 18, true)  -- Enter
            DisableControlAction(0, 22, true)  -- Space
            DisableControlAction(0, 176, true) -- Enter/Return
            DisableControlAction(0, 249, true) -- N

            -- Disable movement
            DisableControlAction(0, 30, true)  -- A/D
            DisableControlAction(0, 31, true)  -- W/S
            DisableControlAction(0, 32, true)  -- W
            DisableControlAction(0, 33, true)  -- S
            DisableControlAction(0, 34, true)  -- A
            DisableControlAction(0, 35, true)  -- D

            -- Disable camera movement
            DisableControlAction(0, 1, true)   -- Mouse look
            DisableControlAction(0, 2, true)   -- Mouse look

            -- Disable weapon controls
            DisableControlAction(0, 24, true)  -- Attack
            DisableControlAction(0, 25, true)  -- Aim
            DisableControlAction(0, 47, true)  -- Weapon wheel
            DisableControlAction(0, 58, true)  -- Weapon wheel
        end

        Citizen.Wait(0)
    end
end)

-- =====================================================
-- EVENT HANDLERS
-- =====================================================

-- Store player team when selected
RegisterNetEvent('koth:teamSelected', function(team)
    SetResourceKvp('playerTeam', team)
    print('[KOTH DEATH] Stored player team:', team)
end)

-- Override spawn event to store team
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    -- Extract team from spawn coordinates
    for team, spawn in pairs(DeathSystem.teamSpawns) do
        if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
            SetResourceKvp('playerTeam', team)
            print('[KOTH DEATH] Detected and stored team from spawn:', team)
            break
        end
    end
end)

-- =====================================================
--[[
  Debug commands to test the death system.  These are disabled in
  production to prevent players from accessing internal state.
]]
-- RegisterCommand('deathtest', function()
--     if DeathSystem.isDead then
--         print('[KOTH DEATH] Force respawning player')
--         respawnPlayer()
--     else
--         print('[KOTH DEATH] Player is not dead')
--     end
-- end, false)

-- RegisterCommand('deathstatus', function()
--     print('[KOTH DEATH] Status - Dead:', DeathSystem.isDead, 'Team:', GetResourceKvpString('playerTeam') or 'none')
-- end, false)

--
-- MEDIC REVIVE EVENT
--
-- When a medic successfully revives a downed teammate the server
-- triggers this event on the target client.  We resurrect the
-- player at their current position, reset the death state and
-- restore them to 50% of their maximum health.  This replicates
-- the effect of a teammate reviving you in place rather than
-- forcing a full respawn at the team base.
RegisterNetEvent('koth:reviveClient')
AddEventHandler('koth:reviveClient', function()
    -- Only handle the revive if we are currently marked as dead
    if not DeathSystem.isDead then return end
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local heading = GetEntityHeading(playerPed)
    -- Resurrect the player at their existing location
    NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, heading, true, false)
    -- Restore to half health
    local maxHealth = GetEntityMaxHealth(playerPed)
    local halfHealth = math.floor(maxHealth * 0.5)
    SetEntityHealth(playerPed, halfHealth)
    -- Remove invincibility and restore normal physics.  Previously
    -- the death system left players collision‑proof and unable to
    -- ragdoll which contributed to “god mode” and floating spawn bugs.
    SetEntityInvincible(playerPed, false)
    SetPedCanRagdoll(playerPed, true)
    -- bulletProof, fireProof, explosionProof, collisionProof,
    -- meleeProof, steamProof, drownProof, bulletShockProof all false
    SetEntityProofs(playerPed, false, false, false, false, false, false, false, false)
    -- Clear tasks and unfreeze
    ClearPedTasksImmediately(playerPed)
    FreezeEntityPosition(playerPed, false)
    -- Reset death system state
    DeathSystem.isDead = false
    DeathSystem.respawnHoldTime = 0
    DeathSystem.respawnProgress = 0
    DeathSystem.hasReleasedE = true
    DeathSystem.hasFrozenOnGround = false
    -- Hide the death screen UI
    SendNUIMessage({ action = 'hideDeathScreen' })

    -- Notify the server that we have been revived.  This removes us from
    -- the list of downed players maintained on each client so that
    -- medics no longer see a revive prompt for us.
    local myServerId = GetPlayerServerId(PlayerId())
    TriggerServerEvent('koth:playerRevived', myServerId)
    -- Reapply team and class appearance after a short delay
    Citizen.SetTimeout(500, function()
        local team = GetResourceKvpString('playerTeam') or 'red'
        TriggerEvent('koth:applyTeamAppearance', team)
    end)
end)

--[[
  Debug command to print detailed damage tracking information.  Disabled
  for players.
]]
-- RegisterCommand('damageinfo', function()
--     print('[KOTH DEATH] === DAMAGE TRACKING INFO ===')
--     print('[KOTH DEATH] Last Damage Source:', DeathSystem.lastDamageSource or 'None')
--     if DeathSystem.lastDamageSource then
--         local timeSinceDamage = (GetGameTimer() - DeathSystem.lastDamageTime) / 1000
--         print('[KOTH DEATH] Time Since Last Damage:', string.format('%.1f seconds ago', timeSinceDamage))
--
--         -- Try to get the player name
--         for _, playerId in ipairs(GetActivePlayers()) do
--             if GetPlayerServerId(playerId) == DeathSystem.lastDamageSource then
--                 local playerName = GetPlayerName(playerId) or 'Unknown'
--                 print('[KOTH DEATH] Last Damager Name:', playerName)
--                 break
--             end
--         end
--     end
--     print('[KOTH DEATH] ========================')
-- end, false)

print('[KOTH DEATH] Death system loaded successfully')
