-- SQL script to create the VIP weapon skins table for KOTH

-- This table stores VIP players' weapon skin preferences
-- Each player can have one skin preference per weapon type
-- The skin data includes tint/color information

CREATE TABLE IF NOT EXISTS koth_vip_skins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    txid VARCHAR(50) NOT NULL,
    weapon_hash VARCHAR(20) NOT NULL,
    tint_id INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_player_weapon (txid, weapon_hash),
    INDEX idx_txid (txid),
    INDEX idx_weapon_hash (weapon_hash)
);

-- Insert some example data (optional)
-- INSERT INTO koth_vip_skins (txid, weapon_hash, tint_id) VALUES 
-- ('license:example123', 'WEAPON_CARBINER<PERSON>LE', 2),
-- ('license:example123', 'WEAPON_PISTOL', 1);
