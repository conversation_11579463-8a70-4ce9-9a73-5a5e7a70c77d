<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP Weapon Skin Changer</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="skinChanger" class="skin-changer-container" style="display: none;">
        <!-- Header -->
        <div class="header">
            <div class="title">
                <span class="vip-badge">VIP</span>
                <h1>Weapon Skin Changer</h1>
            </div>
            <button id="closeBtn" class="close-btn">&times;</button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Weapon Info Panel -->
            <div class="weapon-info-panel">
                <div class="weapon-icon">
                    <div id="weaponIcon" class="weapon-icon-placeholder">
                        🔫
                    </div>
                </div>
                <div class="weapon-details">
                    <h2 id="weaponName">Weapon Name</h2>
                    <p id="weaponHash">Hash: 0x00000000</p>
                    <div class="current-skin">
                        <span>Current Skin:</span>
                        <span id="currentSkinName">Default</span>
                    </div>
                </div>
            </div>

            <!-- Skin Selection Panel -->
            <div class="skin-selection-panel">
                <h3>Available Skins</h3>
                <div class="skin-grid" id="skinGrid">
                    <!-- Skin options will be populated by JavaScript -->
                </div>
            </div>

            <!-- Preview Panel -->
            <div class="preview-panel">
                <h3>Live Preview</h3>
                <div class="preview-container">
                    <div class="preview-info">
                        <p>🎯 3D Preview Active</p>
                        <p>Look around to see your weapon with the selected skin!</p>
                    </div>
                    <div class="preview-controls">
                        <button id="resetPreview" class="preview-btn">Reset View</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button id="cancelBtn" class="action-btn cancel-btn">Cancel</button>
            <button id="applyBtn" class="action-btn apply-btn">Apply Skin</button>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner"></div>
            <p>Applying skin...</p>
        </div>
    </div>

    <!-- Notification System -->
    <div id="notification" class="notification" style="display: none;">
        <div class="notification-content">
            <span id="notificationText"></span>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
