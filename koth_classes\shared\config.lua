Config = {}

-- Class definitions
Config.Classes = {
    assault = {
        name = "Assault",
        description = "Standard soldier class with balanced stats",
        requiredLevel = 1,
        abilities = {
            {
                name = "Ammo Bag",
                slot = 5,
                icon = "images/ammo_allammo.png",
                cooldown = 60, -- 60 seconds
                resupplyRadius = 5.0,
                duration = 30, -- Ammo bag lasts 30 seconds
                prop = "prop_box_ammo03a" -- Ammo box prop
            }
        }
    },
    medic = {
        name = "Medic",
        description = "Support class that can deploy healing stations",
        requiredLevel = 5,
        abilities = {
            {
                name = "Med Bag",
                slot = 5,
                icon = "images/ifak.png",
                cooldown = 60, -- 60 seconds
                healRadius = 5.0,
                healAmount = 5, -- HP per second
                duration = 30, -- Healing zone lasts 30 seconds
                prop = "prop_ld_health_pack" -- Health pack prop
            }
        }
    },
    engineer = {
        name = "Engineer",
        description = "Technical class with deployable equipment",
        requiredLevel = 15,
        abilities = {
            -- Engineer repair ability.  Allows the engineer to repair nearby vehicles and
            -- helicopters when using the ability.  The ability is triggered via the
            -- 5th hotbar slot and uses a wrench prop while repairing.  It takes
            -- 10 seconds to complete the repair and has a 2 minute cooldown.
            {
                name = "Repair Tool",
                slot = 5,
                icon = "images/repair_tool.png",
                cooldown = 120, -- 2 minutes
                duration = 10, -- seconds to complete the repair
                repairRange = 3.0, -- distance within which a vehicle can be repaired
                prop = "prop_cs_wrench" -- GTA model used as the repair tool
            }
        }
    },
    heavy = {
        name = "Heavy",
        description = "Tank class with increased armor",
        requiredLevel = 25,
        abilities = {
            {
                name = "Armor Kit",
                slot = 5,
                -- Use a custom heavy armour icon.  This image lives in the
                -- hotbar's images folder and replaces the default
                -- lightarmour.png.  Players will see a bulletproof vest
                -- icon when equipping the Heavy class ability.
                icon = "images/heavy_armour.png",
                cooldown = 300, -- 5 minutes (300 seconds)
                armorAmount = 100 -- 100% armor
            }
        }
    },
    scout = {
        name = "Scout",
        description = "Precision marksman with stealth capabilities",
        requiredLevel = 35,
        abilities = {}
    }
}

-- Visual settings
Config.HealingZone = {
    color = { r = 0, g = 255, b = 0, a = 100 }, -- Green color
    markerType = 25, -- MarkerTypeHorizontalCircleSkinny
    bobUpAndDown = false,
    rotate = false
}

Config.AmmoZone = {
    color = { r = 255, g = 165, b = 0, a = 100 }, -- Orange color
    markerType = 25, -- MarkerTypeHorizontalCircleSkinny
    bobUpAndDown = false,
    rotate = false
}

-- Debug mode
Config.Debug = true
