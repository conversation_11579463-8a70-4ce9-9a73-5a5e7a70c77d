// KOTH Zone System Fix - UI Handler
console.log('[KOTH FIX] Zone UI handler loaded');

// Handle zone status updates
window.addEventListener('message', function(event) {
    const data = event.data;
    
    // Handle zone status updates
    if (data.action === 'updateZoneStatus') {
        updateKothZoneDisplay(data);
    }
    
    // Handle zone control changes
    if (data.action === 'zoneControlChanged') {
        updateZoneControl(data.team);
    }
    
    // Handle showing zone UI when player enters
    if (data.action === 'updateKothZone') {
        showZoneUI(data.zoneData);
    }
    
    // Handle hiding zone UI when player leaves
    if (data.action === 'hideKothZone') {
        hideZoneUI();
    }
});

// Update the KOTH zone display with current status
function updateKothZoneDisplay(status) {
    const zoneStatus = document.getElementById('koth-zone-status');
    if (!zoneStatus) return;
    
    // Show the zone status
    zoneStatus.style.display = 'block';
    
    const controlText = document.getElementById('koth-control-text');
    
    if (controlText) {
        // Remove all team classes
        controlText.classList.remove('red-control', 'green-control', 'blue-control', 'contested');
        
        // Update control text based on status
        if (status.isContested) {
            controlText.textContent = 'CONTESTED';
            controlText.classList.add('contested');
        } else if (status.controllingTeam) {
            controlText.textContent = status.controllingTeam.toUpperCase() + ' CONTROLLED';
            controlText.classList.add(status.controllingTeam + '-control');
        } else {
            controlText.textContent = 'NEUTRAL';
        }
    }
    
    console.log('[KOTH FIX] Zone status updated:', status);
}

// Update zone control (for instant color changes)
function updateZoneControl(team) {
    const zoneHeader = document.querySelector('.koth-zone-name');
    if (zoneHeader) {
        if (team) {
            zoneHeader.textContent = 'QUARRY ZONE - ' + team.toUpperCase();
            zoneHeader.style.color = getTeamColor(team);
        } else {
            zoneHeader.textContent = 'QUARRY ZONE - NEUTRAL';
            zoneHeader.style.color = '#808080';
        }
    }
}

// Show zone UI when player is in zone
function showZoneUI(zoneData) {
    const zoneStatus = document.getElementById('koth-zone-status');
    if (zoneStatus) {
        zoneStatus.style.display = 'block';
        
        // Update with zone data
        if (zoneData) {
            updateKothZoneDisplay({
                controllingTeam: zoneData.controlling,
                captureProgress: zoneData.progress || 0,
                captureThreshold: zoneData.threshold || 10,
                isContested: zoneData.contested || false
            });
        }
    }
}

// Hide zone UI when player leaves zone
function hideZoneUI() {
    const zoneStatus = document.getElementById('koth-zone-status');
    if (zoneStatus) {
        zoneStatus.style.display = 'none';
    }
}

// Get team color
function getTeamColor(team) {
    const colors = {
        red: '#ff6b6b',
        green: '#51cf66',
        blue: '#339af0'
    };
    return colors[team] || '#808080';
}

// Initialize on load
document.addEventListener('DOMContentLoaded', function() {
    console.log('[KOTH FIX] Zone UI initialized');
    
    // Hide zone status by default
    const zoneStatus = document.getElementById('koth-zone-status');
    if (zoneStatus) {
        zoneStatus.style.display = 'none';
    }
});
