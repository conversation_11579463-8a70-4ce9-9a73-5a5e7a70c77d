* { box-sizing: border-box; margin: 0; padding: 0; }
html, body { width: 180px; height: 100%; font-family: 'Segoe UI', sans-serif; }
#overlay { position: fixed; inset: 0; background: rgba(0,0,0,0.3); display: none; align-items: center; justify-content: center; backdrop-filter: blur(2px); }
#team-select, #menu-container { display: none; flex-direction: column; align-items: center; }

/* Enhanced Team Select */
#team-select {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  animation: teamSelectSlideIn 0.6s ease-out;
}

@keyframes teamSelectSlideIn {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.team-select-container {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  min-width: 600px;
}

.team-select-header {
  text-align: center;
  margin-bottom: 30px;
}

.team-select-title {
  color: #fff;
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 1px;
}

.team-select-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  font-weight: 300;
}

.team-buttons-container {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 30px;
}

.team-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  min-width: 160px;
  position: relative;
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
  border-color: rgba(255, 255, 255, 0.3);
}

.team-card[data-team="red"]:hover {
  border-color: #e74c3c;
  box-shadow: 0 15px 40px rgba(231, 76, 60, 0.3);
}

.team-card[data-team="blue"]:hover {
  border-color: #3498db;
  box-shadow: 0 15px 40px rgba(52, 152, 219, 0.3);
}

.team-card[data-team="green"]:hover {
  border-color: #2ecc71;
  box-shadow: 0 15px 40px rgba(46, 204, 113, 0.3);
}

.team-card-inner {
  padding: 25px 20px;
  text-align: center;
  position: relative;
}

.team-color-indicator {
  width: 60px;
  height: 4px;
  margin: 0 auto 15px;
  border-radius: 2px;
}

.red-indicator { background: linear-gradient(90deg, #e74c3c, #c0392b); }
.blue-indicator { background: linear-gradient(90deg, #3498db, #2980b9); }
.green-indicator { background: linear-gradient(90deg, #2ecc71, #27ae60); }

.team-info {
  margin-bottom: 20px;
}

.team-name {
  color: #fff;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 10px;
  letter-spacing: 1px;
}

.team-count-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.team-count {
  color: #fff;
  font-size: 2rem;
  font-weight: 800;
  line-height: 1;
}

.team-count-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 1px;
}

.team-select-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.team-card:hover .team-select-btn {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.team-select-footer {
  text-align: center;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.loading-indicator.hidden {
  opacity: 0;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: loadingPulse 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 400;
}

/* Responsive design for team select */
@media (max-width: 900px) {
  .team-select-container {
    min-width: 500px;
    padding: 30px;
  }

  .team-buttons-container {
    flex-direction: column;
    align-items: center;
  }

  .team-card {
    min-width: 200px;
  }
}

@media (max-width: 600px) {
  .team-select-container {
    min-width: 90vw;
    padding: 25px;
  }

  .team-select-title {
    font-size: 2.2rem;
  }
}

/* Menu Container */
#menu-container { background: #1e1e1e; padding: 20px; border-radius: 8px; width: 80%; max-width: 600px; color: #fff; position: relative; }
#close-btn { position: absolute; top: 10px; right: 10px; background: transparent; border: none; color: #fff; font-size: 1.5rem; cursor: pointer; }
#menu-title { text-align: center; margin-bottom: 20px; font-size: 2rem; }
#items { display: grid; grid-template-columns: repeat(auto-fill, minmax(120px,1fr)); gap: 15px; }
.item-card { background: #2a2a2a; padding: 10px; border-radius: 6px; display: flex; flex-direction: column; align-items: center; text-align: center; }
.item-card img { width: 80px; height: 80px; object-fit: cover; margin-bottom: 10px; }
.item-card .label { font-size: 1rem; margin-bottom: 5px; }
.item-card .action-btn { margin-top: auto; padding: 5px 8px; border: none; border-radius: 4px; cursor: pointer; background: #3498db; color: #fff; }
.item-card .action-btn.secondary { background: #27ae60; }

/* WEAPONS SHOP STYLING */
#weapons-shop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
}

.shop-header {
  /*
    Lay out the shop header elements cleanly.  We use flexbox for the
    title and money elements, but mark the container as relative so a
    centred level indicator can be absolutely positioned.  Extra right
    padding is applied to ensure the money display does not encroach on
    the close button that sits outside of the header.  Without this
    padding the money label could overlap the close icon on narrow
    resolutions.  The larger value (120px) accounts for the button's
    width and some breathing room.
  */
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 20px 40px;
  /* Reserve space on the right for the close button (40px button + 40px margin + extra) */
  padding-right: 120px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid #333;
}

.shop-title {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 2px;
  color: #fff;
}

.shop-money {
  font-size: 20px;
  font-weight: bold;
  color: #00ff00;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #333;
}

.shop-search {
  display: flex;
  align-items: center;
  padding: 20px 40px;
  gap: 10px;
}

#weapon-search {
  flex: 1;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #333;
  border-radius: 5px;
  padding: 12px 16px;
  color: white;
  font-size: 14px;
}

#weapon-search::placeholder {
  color: #666;
}

.refresh-btn {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #333;
  border-radius: 5px;
  padding: 12px;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.weapons-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 20px;
  padding: 20px 40px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.weapon-card {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.weapon-card:hover {
  border-color: #555;
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.weapon-image {
  width: 180px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 10px;
  filter: brightness(0.9);
  position: relative; /* enable absolute positioning for overlays */
}

.weapon-name {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  min-height: 20px;
}

.weapon-price {
  font-size: 16px;
  font-weight: bold;
  color: #00ff00;
  margin-bottom: 12px;
}

.weapon-buy-btn {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid #444;
  border-radius: 20px;
  padding: 8px 16px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.weapon-buy-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #666;
}

.weapon-buy-btn.disabled {
  background: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

.weapon-buy-btn.disabled:hover {
  background: #666;
  border-color: #444;
}

.weapon-card.unaffordable {
  opacity: 0.6;
  filter: grayscale(50%);
}

.shop-close {
  position: absolute;
  top: 20px;
  right: 40px;
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid #ff0000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.shop-close:hover {
  background: rgba(255, 0, 0, 0.4);
}

/* VEHICLES SHOP STYLING (Same as weapons shop) */
#vehicles-shop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 20px;
  padding: 20px 40px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.vehicle-card {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

/* Locked overlay for VIP or level-restricted items */
.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  color: #f0c040;
  font-weight: 700;
  font-size: 0.9rem;
  text-transform: uppercase;
  border-radius: 8px;
  pointer-events: none;
}

/* Overlay used to display the required player level on locked vehicle cards.
   This appears centered over the vehicle image when the player's level is below
   the required level.  The text is red to match other lock indicators and
   does not capture mouse input. */
.level-lock-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4444;
  font-weight: 700;
  font-size: 1rem;
  pointer-events: none;
  text-transform: uppercase;
}

/* Text for locked vehicles in price area */
.locked-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  text-align: center;
  margin-top: 4px;
}

/* Dim locked vehicle cards */
.vehicle-card.locked {
  opacity: 0.6;
}

/* Dim locked weapon items */
.weapon-item.locked {
  opacity: 0.5;
  position: relative;
}

.weapon-item.locked .lock-overlay {
  color: #f0c040;
  font-size: 0.8rem;
}

.vehicle-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 180px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transition: left 0.6s ease;
}

.vehicle-card:hover::before {
  left: 100%;
}

.vehicle-card:hover {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
}

.vehicle-image {
  width: 180px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 10px;
  filter: brightness(0.9);
  position: relative; /* enable absolute positioning for overlays */
}

.vehicle-name {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  min-height: 20px;
}

.vehicle-price {
  font-size: 16px;
  font-weight: bold;
  color: #00ff00;
  margin-bottom: 8px;
}

.vehicle-rent-price {
  font-size: 14px;
  color: #ffaa00;
  margin-bottom: 12px;
}

.vehicle-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
}

.vehicle-buy-btn, .vehicle-rent-btn {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  border: 2px solid #444;
  border-radius: 25px;
  padding: 10px 20px;
  color: white;
  font-size: 13px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.vehicle-buy-btn::before, .vehicle-rent-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 180px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.vehicle-buy-btn:hover::before, .vehicle-rent-btn:hover::before {
  left: 100%;
}

.vehicle-buy-btn {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
  border-color: #4CAF50;
  color: #4CAF50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.vehicle-buy-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.4), rgba(76, 175, 80, 0.2));
  border-color: #66BB6A;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.vehicle-rent-btn {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.3), rgba(255, 152, 0, 0.1));
  border-color: #FF9800;
  color: #FF9800;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
}

.vehicle-rent-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.4), rgba(255, 152, 0, 0.2));
  border-color: #FFB74D;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

/* Spawn button styling for owned vehicles.  Uses a blue colour to distinguish
   from buy (green) and rent (orange) actions. */
.vehicle-spawn-btn {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.3), rgba(33, 150, 243, 0.1));
  border: 2px solid #2196F3;
  border-radius: 25px;
  padding: 10px 20px;
  color: #2196F3;
  font-size: 13px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.vehicle-spawn-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 180px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.vehicle-spawn-btn:hover::before {
  left: 100%;
}

.vehicle-spawn-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.4), rgba(33, 150, 243, 0.2));
  border-color: #64B5F6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.vehicle-spawn-btn.disabled {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.3), rgba(102, 102, 102, 0.1));
  border-color: #666;
  color: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

.vehicle-buy-btn.disabled, .vehicle-rent-btn.disabled {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.3), rgba(102, 102, 102, 0.1));
  border-color: #666;
  cursor: not-allowed;
  opacity: 0.5;
  color: #999;
  box-shadow: none;
}

.vehicle-buy-btn.disabled:hover, .vehicle-rent-btn.disabled:hover {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.3), rgba(102, 102, 102, 0.1));
  border-color: #666;
  transform: none;
  box-shadow: none;
}

.vehicle-buy-btn.disabled::before, .vehicle-rent-btn.disabled::before {
  display: none;
}

.vehicle-card.unaffordable {
  opacity: 0.6;
  filter: grayscale(50%);
}

/* NEW CLASSES SELECTION UI - Matching Your Design */
#classes-selection {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
  z-index: 10000;
}

.classes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.classes-title {
  font-size: 24px;
  font-weight: normal;
  color: #fff;
  margin: 0;
  letter-spacing: 1px;
  flex: 1;
}

.classes-money {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  font-weight: 600;
  color: #7bed9f;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 20px;
  border-radius: 20px;
  border: 2px solid rgba(123, 237, 159, 0.3);
}

.classes-close {
  background: transparent;
  border: 2px solid #ff4444;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #ff4444;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.classes-close:hover {
  background: rgba(255, 68, 68, 0.2);
  transform: rotate(90deg);
}

/*
 * Classes Selection Grid
 *
 * Reworked to match the style of the weapons/vehicles shop.  The grid now
 * uses a responsive layout and each class card has a dark translucent
 * background with subtle borders and shadows.  Unlock text colours are
 * unified with the rest of the UI (green for unlocked, amber for locked).
 */

.classes-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 40px;
  justify-content: center;
  align-items: center;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.class-card {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  width: 180px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.class-card:hover {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
}

.class-image-container {
  width: 180px;
  height: 80px;
  overflow: hidden;
  position: relative;
}

.class-image {
  width: 180px;
  height: 100%;
  object-fit: contain;
  filter: brightness(0.9);
  margin-bottom: 10px;
  position: relative;
  transition: filter 0.3s ease;
}

.class-card:hover .class-image {
  filter: brightness(1.1);
}

/* Lock overlay for locked classes */
.class-lock-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f0c040;
  font-size: 1rem;
  pointer-events: none;
  z-index: 2;
}

.lock-icon {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.7);
}

.class-info {
  padding: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.class-name {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.class-unlock {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 0;
  color: #4CAF50; /* default to green for unlocked */
}

/* Locked class unlock text uses amber colour */
.class-card.locked .class-unlock {
  color: #FFA000;
}

/* NEW WEAPON SHOP UI - Matching Your Design */
#weapon-shop-new {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #0a0a0a;
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
  z-index: 10001;
}

.weapon-shop-header {
  display: flex;
  /* Spread items with a small gap instead of full spacing so that
     multiple indicators (level and money) can align near the right
     while the title remains to the left. */
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  padding: 20px 40px;
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.weapon-shop-title {
  font-size: 24px;
  font-weight: normal;
  color: #fff;
  margin: 0;
}

.weapon-shop-money {
  font-size: 20px;
  font-weight: bold;
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
  padding: 8px 20px;
  border-radius: 20px;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

/* Display the class level indicator in the weapon shop header.  This
   element shows the player’s current class level for the selected
   class.  The styling is similar to the money display but uses an
   orange theme to differentiate it. */
.weapon-shop-level {
  font-size: 20px;
  font-weight: bold;
  color: #FFA500;
  background: rgba(255, 165, 0, 0.1);
  padding: 8px 20px;
  border-radius: 20px;
  border: 1px solid rgba(255, 165, 0, 0.3);
  margin-right: 10px;
}

.weapon-shop-close {
  background: transparent;
  border: 2px solid #ff4444;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #ff4444;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.weapon-shop-close:hover {
  background: rgba(255, 68, 68, 0.2);
  transform: rotate(90deg);
}

.weapon-categories {
  display: flex;
  gap: 0;
  padding: 0 40px;
  background: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.weapon-category {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  padding: 15px 30px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.weapon-category:hover {
  color: rgba(255, 255, 255, 0.8);
}

.weapon-category.active {
  color: #00aaff;
  border-bottom-color: #00aaff;
}

.weapon-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 30px 40px;
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}

.weapon-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.weapon-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.weapon-item.locked {
  opacity: 0.5;
  cursor: not-allowed;
}

.weapon-item.locked:hover {
  transform: none;
}

.weapon-item.spawned {
  border-color: #00aaff;
  background: rgba(0, 170, 255, 0.1);
}

.weapon-icon {
  width: 80px;
  height: 40px;
  object-fit: contain;
  filter: brightness(0.8);
}

.weapon-item.locked .weapon-icon {
  filter: brightness(0.4) grayscale(1);
}

.weapon-details {
  flex: 1;
}

.weapon-name {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin: 0 0 5px 0;
}

.weapon-unlock {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
}

.weapon-item.locked .weapon-unlock {
  color: #ff4444;
}

.weapon-action {
  margin-left: auto;
}

.weapon-spawn-btn {
  background: #00aaff;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.weapon-spawn-btn:hover {
  background: #0088cc;
  transform: scale(1.05);
}

.weapon-item.locked .weapon-spawn-btn {
  display: none;
}

.weapon-lock-icon {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.3);
}

/* Scrollbar styling */
.weapon-grid::-webkit-scrollbar {
  width: 8px;
}

.weapon-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.weapon-grid::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.weapon-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* PERMANENT GAME HUD SYSTEM - BIGGER DESIGN */
#game-hud {
  position: fixed;
  bottom: 20px;
  right: 20px;
  color: #ffffff;
  font-family: 'Segoe UI', 'Arial', sans-serif;
  z-index: 1000;
  display: block;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
  transform: scale(1.3); /* 30% bigger as requested */
  transform-origin: bottom right;
}

/* Zone Control Points - Compact Grey Cards */
.zone-points {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  justify-content: space-between;
  width: 180px;
}

.zone-box {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 6px 8px;
  text-align: center;
  min-width: 40px;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.zone-box:hover {
  background: rgba(50, 50, 50, 0.95);
  border-color: rgba(255, 255, 255, 0.3);
}

.red-zone {
  border-left: 3px solid #ff6b6b;
}

.green-zone {
  border-left: 3px solid #51cf66;
}

.blue-zone {
  border-left: 3px solid #339af0;
}

.zone-number {
  font-size: 16px; /* Increased for bigger HUD */
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 2px;
}

.zone-label {
  font-size: 10px; /* Increased for bigger HUD */
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Player Info Section - Compact Grey Card */
.player-info {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 12px 14px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  min-width: 180px;
}

.player-info:hover {
  background: rgba(50, 50, 50, 0.95);
  border-color: rgba(255, 255, 255, 0.3);
}

.player-name {
  font-size: 15px; /* Increased for bigger HUD */
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
  text-align: center;
}

.player-money {
  font-size: 18px; /* Increased for bigger HUD */
  font-weight: 700;
  color: #51cf66;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
  text-align: center;
}

.player-stats {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  min-width: 50px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.stat-label {
  font-size: 10px; /* Increased for bigger HUD */
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 14px; /* Increased for bigger HUD */
  color: #ffffff;
  font-weight: 600;
}

/* XP Progress Bar */
.xp-progress {
  margin-top: 4px;
}

.xp-bar {
  width: 180px;
  height: 5px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.xp-fill {
  height: 100%;
  background: linear-gradient(90deg, #339af0, #51cf66);
  transition: width 0.3s ease;
  border-radius: 3px;
  position: relative;
  width: 0%; /* Initialize to 0% to prevent showing full bar on load */
}

.xp-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: xpShimmer 2s infinite;
}

@keyframes xpShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.xp-text {
  font-size: 10px; /* Increased for bigger HUD */
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-top: 2px;
  font-weight: 400;
}

/* Class XP Progress Bar */
.class-xp-progress {
  margin-top: 2px;
}

/*
  Position the class XP bar within the weapon shop so it sits
  neatly under the level indicator.  By constraining the width and
  centring it with auto margins we ensure the bar aligns with the
  centrally positioned level display.  This rule only targets the
  weapon shop's progress bar (shop-class-xp) and does not affect
  other XP bars in the UI.
*/
#shop-class-xp {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 180px;
  margin: 4px auto 0 auto;
}

.class-xp-bar {
  width: 180px;
  height: 5px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.class-xp-fill {
  height: 100%;
  /* Use a distinct colour gradient for class XP (orange/yellow) */
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
  transition: width 0.3s ease;
  border-radius: 3px;
  position: relative;
  width: 0%;
}

.class-xp-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: classXpShimmer 2s infinite;
}

@keyframes classXpShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.class-xp-text {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-top: 2px;
  font-weight: 400;
}

/* Weapon shop header class level display */
/*
  Weapon shop header class level display.  Unlike the title and money
  elements, the level indicator should float in the centre of the
  header irrespective of the lengths of the other items.  We achieve
  this by absolutely positioning the element at the horizontal centre
  of the header.  A translation is used to centre the element's own
  width.  Vertical centring is also applied so it remains aligned with
  the other content.  Any default margins are removed so the element
  doesn't push adjacent content around.
*/
.shop-level {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
  font-size: 14px;
  color: #f59e0b;
  font-weight: 600;
  white-space: nowrap;
}

/* Small label for the class level in the weapon shop header.  This sits
   above the "LVL N" text and provides context that the level refers
   specifically to the class level rather than overall player level. */
.shop-class-level-label {
  display: block;
  font-size: 9px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  margin-bottom: 2px;
}

/* Team Player Counts - Compact Indicators */
.team-player-counts {
  display: flex;
  gap: 6px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  justify-content: center;
}

.team-players-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 3px 6px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.2s ease;
}

.team-players-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.team-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.red-dot { background: #ff6b6b; }
.green-dot { background: #51cf66; }
.blue-dot { background: #339af0; }

.team-count-text {
  font-size: 11px; /* Increased for bigger HUD */
  color: #ffffff;
  font-weight: 600;
  min-width: 12px;
  text-align: center;
}

/* Health Bar Section - REMOVED as requested */
.health-section {
  display: none !important;
  visibility: hidden !important;
}

.health-bar {
  display: none !important;
}

.health-fill {
  display: none !important;
}

.health-text {
  display: none !important;
}

.health-label {
  display: none !important;
}

/* Remove health shimmer animation */
@keyframes healthShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* KOTH Zone Status (When Active) */
.koth-zone-status {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 12px 14px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  min-width: 180px;
  display: none; /* Will be shown via JavaScript when in zone */
}

.koth-zone-status:hover {
  background: rgba(50, 50, 50, 0.95);
  border-color: rgba(255, 255, 255, 0.3);
}

.koth-zone-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.koth-crown {
  font-size: 16px;
  filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));
}

.koth-zone-name {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.koth-progress {
  margin-top: 8px;
}

.koth-progress-bar {
  width: 180px;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  margin-bottom: 4px;
}

.koth-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ff8787);
  transition: width 0.3s ease;
  border-radius: 3px;
  position: relative;
}

.koth-progress-fill.red-team {
  background: linear-gradient(90deg, #ff6b6b, #ff8787);
}

.koth-progress-fill.green-team {
  background: linear-gradient(90deg, #51cf66, #69db7c);
}

.koth-progress-fill.blue-team {
  background: linear-gradient(90deg, #339af0, #4dabf7);
}

.koth-progress-fill.contested {
  background: linear-gradient(90deg, #868e96, #adb5bd);
  animation: contestedPulse 1s ease-in-out infinite;
}

@keyframes contestedPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.koth-progress-text {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* New control text style without progress bar */
.koth-control-text {
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  color: #808080;
  transition: color 0.3s ease;
}

.koth-control-text.red-control {
  color: #ff6b6b;
  text-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
}

.koth-control-text.green-control {
  color: #51cf66;
  text-shadow: 0 0 5px rgba(81, 207, 102, 0.5);
}

.koth-control-text.blue-control {
  color: #339af0;
  text-shadow: 0 0 5px rgba(51, 154, 240, 0.5);
}

.koth-control-text.contested {
  color: #ff6b35;
  text-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
}


/* Responsive adjustments */
@media (max-width: 1200px) {
  #game-hud {
    bottom: 15px;
    right: 15px;
  }
}

@media (max-width: 900px) {
  #game-hud {
    bottom: 10px;
    right: 10px;
  }

  .zone-points {
    gap: 6px;
  }

  .zone-box {
    padding: 6px 10px;
    min-width: 45px;
  }

  .zone-number {
    font-size: 16px;
  }

  .team-player-counts {
    gap: 8px;
  }
}

/* DEATH SCREEN SYSTEM */
#death-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  font-family: 'Arial', sans-serif;
}

.death-content {
  text-align: center;
  color: white;
  max-width: 600px;
  padding: 40px;
}

.death-title {
  font-size: 48px;
  font-weight: bold;
  color: white;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 2px;
}

.respawn-section {
  margin-bottom: 30px;
}

.respawn-instruction {
  font-size: 18px;
  color: white;
  margin-bottom: 15px;
  font-weight: bold;
}

.key-highlight {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

.respawn-progress {
  width: 180px;
  max-width: 400px;
  margin: 0 auto;
}

.respawn-bar {
  width: 180px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.respawn-fill {
  height: 100%;
  background: linear-gradient(90deg, #dc3545 0%, #ff6b6b 100%);
  width: 0%;
  transition: width 0.1s linear;
  border-radius: 10px;
}

.bleedout-section {
  margin-bottom: 40px;
}

.bleedout-text {
  font-size: 20px;
  color: white;
  font-weight: bold;
  margin-bottom: 8px;
}

.medic-call {
  font-size: 14px;
  color: #ccc;
}

.medic-highlight {
  color: #28a745;
  font-weight: bold;
  text-transform: uppercase;
}

.killer-info {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  width: 180px;
  text-align: center;
}

.killer-text {
  font-size: 16px;
  color: #ccc;
}

.killer-id {
  color: #007bff;
  font-weight: bold;
}

.killer-name {
  color: #007bff;
  font-weight: bold;
}

/* Responsive adjustments for death screen */
@media (max-width: 900px) {
  .death-title {
    font-size: 36px;
  }

  .respawn-instruction {
    font-size: 16px;
  }

  .bleedout-text {
    font-size: 18px;
  }

  .killer-info {
    bottom: 60px;
  }
}

/* ENHANCED KILL REWARD POPUP SYSTEM - Small and Clean */
#kill-reward-popup {
  position: fixed;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  display: none;
  z-index: 10000;
  pointer-events: none;
  font-family: 'Arial', sans-serif;
}

.kill-reward-content {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  padding: 8px 15px;
  min-width: 200px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  position: relative;
  overflow: hidden;
}

.kill-reward-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 180px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
  animation: killRewardShimmer 2s ease-in-out;
}

@keyframes killRewardShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.kill-reward-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-bottom: 5px;
  position: relative;
  z-index: 1;
}

.kill-reward-plus {
  color: #4CAF50;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
  animation: killRewardPulse 0.4s ease-out;
}

@keyframes killRewardPulse {
  0% { transform: scale(0.5); opacity: 0; }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); opacity: 1; }
}

.kill-reward-money {
  color: #4CAF50;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
  font-family: 'Courier New', monospace;
  animation: killRewardSlideIn 0.4s ease-out 0.1s both;
}

@keyframes killRewardSlideIn {
  0% { transform: translateX(-20px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

.kill-reward-confirmed {
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  animation: killRewardFadeIn 0.5s ease-out 0.2s both;
}

@keyframes killRewardFadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

.kill-reward-details {
  position: relative;
  z-index: 1;
}

.kill-reward-xp {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-bottom: 4px;
  animation: killRewardSlideUp 0.4s ease-out 0.3s both;
}

@keyframes killRewardSlideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

.xp-plus {
  color: #FFD700;
  font-size: 12px;
  font-weight: bold;
}

.xp-amount {
  color: #FFD700;
  font-size: 12px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.xp-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
  font-weight: 500;
  text-transform: lowercase;
}

/* VIP tag inside kill reward header */
.vip-kill-tag {
  margin-left: 8px;
  color: #ffd700;
  font-size: 11px;
  font-weight: 700;
  text-shadow: 0 0 6px rgba(255, 215, 0, 0.4);
  border: 1px solid rgba(255, 215, 0, 0.35);
  border-radius: 3px;
  padding: 1px 4px;
}

.kill-reward-zone {
  background: rgba(255, 107, 53, 0.2);
  color: #ff6b35;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: normal;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  margin-top: 4px;
  box-shadow: none;
  animation: killRewardZoneBounce 0.5s ease-out 0.4s both;
  border: 1px solid rgba(255, 107, 53, 0.3);
  display: inline-block;
}

@keyframes killRewardZoneBounce {
  0% { transform: scale(0.8) translateY(10px); opacity: 0; }
  50% { transform: scale(1.1) translateY(-2px); }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}

.zone-text {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* Enhanced animations for zone kills */
#kill-reward-popup.zone-kill .kill-reward-content {
  border-color: rgba(255, 107, 53, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8), 0 0 25px rgba(255, 107, 53, 0.4);
  animation: killRewardZoneGlow 2s ease-in-out infinite alternate;
}

@keyframes killRewardZoneGlow {
  0% { box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8), 0 0 25px rgba(255, 107, 53, 0.4); }
  100% { box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8), 0 0 35px rgba(255, 107, 53, 0.6); }
}

#kill-reward-popup.zone-kill .kill-reward-content::before {
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.15), transparent);
}

/* Show/hide animations */
#kill-reward-popup.kill-reward-show {
  animation: killRewardPopupShow 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes killRewardPopupShow {
  0% {
    transform: translateX(-50%) translateY(30px) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: translateX(-50%) translateY(0) scale(1);
    opacity: 1;
  }
}

#kill-reward-popup.kill-reward-hide {
  animation: killRewardPopupHide 0.5s ease-in-out;
}

@keyframes killRewardPopupHide {

/* Daily Reward Modal */
#daily-reward {
  position: fixed;
  top: 50%; left: 50%; transform: translate(-50%, -50%);
  z-index: 9999; display: none;
}
#daily-reward .daily-container {
  background: rgba(0,0,0,0.85);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px; padding: 24px 28px; min-width: 420px;
  color: #fff; box-shadow: 0 12px 40px rgba(0,0,0,0.6);
}
#daily-reward .daily-header h2 { font-size: 1.8rem; margin-bottom: 4px; }
#daily-reward .daily-header p { color: rgba(255,255,255,0.7); margin-bottom: 16px; }
#daily-reward .daily-reward-cards { display: flex; gap: 16px; margin-bottom: 12px; }
#daily-reward .reward-card { flex: 1; background: rgba(255,255,255,0.06); border: 1px solid rgba(255,255,255,0.12); border-radius: 12px; padding: 16px; text-align: center; }
#daily-reward .reward-label { font-size: 0.9rem; color: rgba(255,255,255,0.75); margin-bottom: 6px; letter-spacing: .5px; }
#daily-reward .reward-value { font-size: 1.6rem; font-weight: 700; }
#daily-reward .daily-vip { display: flex; align-items: center; gap: 8px; color: #ffd25a; margin: 6px 0 10px; }
#daily-reward .vip-badge { background: linear-gradient(90deg, #7a5cff, #ffda6b); color: #000; font-weight: 700; border-radius: 999px; padding: 4px 8px; font-size: 0.8rem; }
#daily-reward .daily-actions { display: flex; justify-content: flex-end; }
#daily-reward .btn-primary { background: #5865f2; border: none; color: #fff; padding: 10px 18px; border-radius: 10px; cursor: pointer; font-weight: 600; letter-spacing: .4px; }
#daily-reward .btn-primary:hover { background: #4752c4; }

  0% {
    transform: translateX(-50%) translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-20px) scale(0.9);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .kill-reward-content {
#zone-bonus-popup {
  position: fixed;
  top: 13%;
  left: 50%;
  transform: translateX(-50%);
  display: none;
  z-index: 10001;
  pointer-events: none;
}
#zone-bonus-popup .zone-bonus-inner {
  background: rgba(255,107,53,0.9);
  color: #fff;
  border-radius: 5px;
  padding: 6px 12px;
  font-weight: 700;
  letter-spacing: .5px;
  box-shadow: 0 6px 24px rgba(0,0,0,.5);
}
#zone-bonus-popup .zone-bonus-label {
  margin-right: 8px;
}
#zone-bonus-popup.show { animation: zoneBonusShow .35s ease-out; }
#zone-bonus-popup.hide { animation: zoneBonusHide .35s ease-in forwards; }
@keyframes zoneBonusShow { from{opacity:0; transform: translateX(-50%) translateY(-8px);} to{opacity:1; transform: translateX(-50%) translateY(0);} }
@keyframes zoneBonusHide { from{opacity:1;} to{opacity:0;} }

    min-width: 300px;
    padding: 18px 25px;
  }

  .kill-reward-money {
    font-size: 24px;
  }

  .kill-reward-plus {
    font-size: 28px;
  }
}

/* LEVEL UP POPUP - Small and at Top */
#levelup-popup {
  position: fixed;
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  display: none;
  z-index: 10001;
}

.levelup-content-small {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 5px;
  padding: 10px 20px;
  min-width: 200px;
  text-align: center;
  animation: levelUpSlide 0.4s ease-out;
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

@keyframes levelUpSlide {
  0% {
    transform: translateY(-30px) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translateY(0) scale(1.05);
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.levelup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-bottom: 8px;
}

.levelup-icon {
  font-size: 16px;
  filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.5));
}

.levelup-title {
  font-size: 14px;
  font-weight: bold;
  color: #FFD700;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.levelup-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: bold;
}

.level-old {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.level-arrow {
  color: #FFD700;
  font-size: 14px;
}

.level-new {
  color: #4CAF50;
  font-size: 16px;
  text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

/* ANIMATIONS */
@keyframes rewardSlideIn {
  from {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes levelUpBounce {
  0% {
    transform: translate(-50%, -50%) scale(0.3);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes zonePulse {
  from {
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.8);
  }
}

/* WEAPON HOTBAR REMOVED - Using separate hotbar resource instead */

/* Weapon Shop Rent Button Styles - Enhanced to match vehicle buttons */
.weapon-buttons {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.weapon-buy-btn,
.weapon-rent-btn {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  border: 2px solid #444;
  border-radius: 25px;
  padding: 10px 16px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.weapon-buy-btn::before, .weapon-rent-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 180px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.weapon-buy-btn:hover::before, .weapon-rent-btn:hover::before {
  left: 100%;
}

.weapon-buy-btn {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
  border-color: #4CAF50;
  color: #4CAF50;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

.weapon-buy-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.4), rgba(76, 175, 80, 0.2));
  border-color: #66BB6A;
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.weapon-rent-btn {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.3), rgba(52, 152, 219, 0.1));
  border-color: #3498db;
  color: #3498db;
  box-shadow: 0 2px 6px rgba(52, 152, 219, 0.2);
}

.weapon-rent-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.4), rgba(52, 152, 219, 0.2));
  border-color: #5dade2;
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
}

.weapon-buy-btn.disabled,
.weapon-rent-btn.disabled {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.3), rgba(102, 102, 102, 0.1));
  border-color: #666;
  cursor: not-allowed;
  opacity: 0.5;
  color: #999;
  box-shadow: none;
}

.weapon-buy-btn.disabled:hover,
.weapon-rent-btn.disabled:hover {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.3), rgba(102, 102, 102, 0.1));
  border-color: #666;
  transform: none;
  box-shadow: none;
}

.weapon-buy-btn.disabled::before,
.weapon-rent-btn.disabled::before {
  display: none;
}

.weapon-rent-price {
  font-size: 12px;
  color: #3498db;
  margin-top: 4px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ATTACHMENT SHOP STYLING */
#attachment-shop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* Lighten the attachment overlay so the in‑game world is slightly more visible */
  background: rgba(0, 0, 0, 0.9);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
  z-index: 10000;
}

.attachment-shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  /* Lighten the header bar to differentiate it from the overlay */
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.attachment-shop-title {
  font-size: 24px;
  font-weight: normal;
  color: #fff;
  margin: 0;
  letter-spacing: 1px;
  flex: 1;
}

.attachment-shop-money {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  font-weight: 600;
  color: #7bed9f;
  /* Lighten the money pill background and border for better contrast */
  background: rgba(0, 0, 0, 0.4);
  padding: 8px 20px;
  border-radius: 20px;
  border: 2px solid rgba(123, 237, 159, 0.4);
}

.attachment-shop-close {
  background: transparent;
  border: 2px solid #ff4444;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #ff4444;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.attachment-shop-close:hover {
  background: rgba(255, 68, 68, 0.2);
  transform: rotate(90deg);
}

.attachment-weapon-info {
  padding: 20px 40px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.attachment-weapon-name {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 5px;
}

.attachment-price-info {
  font-size: 14px;
  color: #7bed9f;
  font-weight: 500;
}

.attachment-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 30px 40px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.attachment-card {
  /* Lighten card background and border for better visibility */
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  /* Add a subtle shadow to lift cards off the background */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.attachment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 180px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transition: left 0.6s ease;
}

.attachment-card:hover::before {
  left: 100%;
}

.attachment-card:hover {
  border-color: rgba(255, 255, 255, 0.3);
  /* Slightly increase brightness on hover */
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
}

.attachment-image {
  /* Increase image size for clarity */
  width: 90px;
  height: 90px;
  object-fit: contain;
  margin: 0 auto 15px;
  filter: brightness(0.9);
}

.attachment-name {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10px;
  min-height: 20px;
}

.attachment-price {
  font-size: 18px;
  font-weight: bold;
  color: #7bed9f;
  margin-bottom: 15px;
}

.attachment-buy-btn {
  /* Slightly brighten the button by default and use a softer border colour */
  background: linear-gradient(135deg, rgba(123, 237, 159, 0.35), rgba(123, 237, 159, 0.15));
  border: 2px solid #95e1d3;
  border-radius: 25px;
  padding: 12px 20px;
  color: #95e1d3;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.attachment-buy-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 180px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.attachment-buy-btn:hover::before {
  left: 100%;
}

.attachment-buy-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(123, 237, 159, 0.4), rgba(123, 237, 159, 0.2));
  border-color: #95e1d3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(123, 237, 159, 0.3);
}

.attachment-buy-btn.disabled {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.3), rgba(102, 102, 102, 0.1));
  border-color: #666;
  cursor: not-allowed;
  opacity: 0.5;
  color: #999;
  box-shadow: none;
}

.attachment-buy-btn.disabled:hover {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.3), rgba(102, 102, 102, 0.1));
  border-color: #666;
  transform: none;
  box-shadow: none;
}

.attachment-buy-btn.disabled::before {
  display: none;
}

.attachment-card.unaffordable {
  opacity: 0.6;
  filter: grayscale(50%);
}

/* Scrollbar styling for attachment grid */
.attachment-grid::-webkit-scrollbar {
  width: 8px;
}

.attachment-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.attachment-grid::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.attachment-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* ------------------------------------------------------------------
   Section headers for vehicle and weapon shops

   These classes style the labels inserted before each category group in
   the vehicle and weapon shops.  They provide clear separation
   between categories and improve the overall readability of the UI.
--------------------------------------------------------------------*/
.vehicle-category-header,
.weapon-category-header {
  width: 180px;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 12px 0 8px;
  padding: 4px 8px;
  text-transform: uppercase;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.03);
  letter-spacing: 0.5px;
}

/* Style for VIP only price text in weapons and vehicles */
.vip-only {
  color: #ffaa00; /* golden yellow to highlight exclusivity */
  font-weight: 600;
}

/* Style for level-locked price text on weapons.  Displayed when the
   player’s class level is below the required level for a weapon. */
.level-locked {
  color: #ff4444; /* red colour to indicate lock */
  font-weight: 600;
}

/* ------------------------------------------------------------------
   Kill feed styling

   The kill feed appears in the top right corner of the screen.  Each
   message displays the killer and victim names with team‑specific
   colours.  Messages fade out after a few seconds and do not capture
   mouse input.  Adjust the width, background opacity and text size
   here to suit your desired look.  Ensure z‑index is high enough so
   messages appear above the HUD.  */
#kill-feed-container {
  position: fixed;
  top: 10%;
  right: 2%;
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  z-index: 10000;
  pointer-events: none;
}

.kill-feed-message {
  background: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
  padding: 4px 8px;
  color: #ffffff;
  font-size: 0.9rem;
  line-height: 1.2;
  opacity: 1;
  transition: opacity 0.5s ease;
}

/* ------------------------------------------------------------------ */
/* Map vote panel styles */
/* This panel appears when players need to vote on the next map. It is
   centred on the screen and contains a title, a row of buttons and a
   countdown timer. The buttons highlight on hover and when selected. */
.map-vote-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* Use a semi‑transparent dark backdrop with subtle border and a
     stronger shadow to give the panel depth.  Increase padding and
     width to make the vote options feel more spacious. */
  background: rgba(0, 0, 0, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 30px 40px;
  z-index: 9999;
  min-width: 550px;
  max-width: 80%;
  color: #ffffff;
  text-align: center;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
}

.map-vote-title {
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 700;
  color: #f0c93b;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
}

.map-vote-options {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.map-vote-option {
  /* Each map vote option is presented as a card with a subtle
     translucent background, rounded corners and inner shadow.  The
     relative positioning allows the vote count badge to be placed
     within.  Cards grow slightly on hover to indicate interactivity. */
  position: relative;
  background: rgba(50, 50, 50, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 18px 24px;
  min-width: 160px;
  flex: 1 1 30%;
  cursor: pointer;
  transition: transform 0.15s ease, box-shadow 0.15s ease, background 0.15s ease;
  color: #ffffff;
  font-size: 20px;
  font-weight: 500;
  user-select: none;
  backdrop-filter: blur(4px);
  text-align: center;
}

.map-vote-option:hover {
  background: rgba(70, 70, 70, 0.85);
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.map-vote-option.selected {
  background: linear-gradient(135deg, #357edd, #2a5da5);
  border-color: #4f91e0;
  box-shadow: 0 8px 20px rgba(79, 145, 224, 0.6);
}

.map-vote-timer {
  font-size: 18px;
  font-weight: 500;
  color: #999999;
}

/* Map name within the vote option */
.map-vote-option .map-name {
  display: block;
  margin-bottom: 4px;
}

/* Vote count badge displayed in the top‑right of each option.  It
   uses a semi‑transparent dark background and white text. */
.map-vote-option .vote-count {
  position: absolute;
  top: 8px;
  right: 10px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  pointer-events: none;
  transition: background 0.2s;
}

/* When a vote option is selected, brighten the badge slightly */
.map-vote-option.selected .vote-count {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* === Prestige Menu Styles === */
/* The prestige menu overlays the entire screen with a semi‑transparent
   background and centres a content panel.  It displays the
   player's prestige rank, token counts and provides buttons to
   prestige or unlock prestige items. */
.prestige-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Arial', sans-serif;
}

.prestige-menu-content {
  background: #2c2c2c;
  padding: 20px 30px;
  border-radius: 8px;
  color: #f5f5f5;
  width: 400px;
  max-width: 90%;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.prestige-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: bold;
}

.prestige-info {
  margin: 4px 0;
  font-size: 16px;
}

.prestige-actions {
  margin-top: 15px;
}

.prestige-menu .prestige-btn {
  background: #444;
  border: none;
  padding: 10px 15px;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  min-width: 180px;
}

.prestige-menu .prestige-btn:disabled {
  background: #666;
  cursor: not-allowed;
}

.prestige-weapons-list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 5px;
}

.prestige-weapons-list button {
  margin: 5px;
  padding: 8px 12px;
  background: #555;
  border: none;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
}

.prestige-weapons-list button:disabled {
  background: #777;
  cursor: not-allowed;
}

/* Prestige vehicles list styling mirrors the weapons list.  Each
   entry appears as a simple button with optional image. */
.prestige-vehicles-list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 5px;
}

.prestige-vehicles-list button {
  margin: 5px;
  padding: 8px 12px;
  background: #555;
  border: none;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
}

.prestige-vehicles-list button:disabled {
  background: #777;
  cursor: not-allowed;
}

/*
 * VIP Bonus Indicator
 *
 * Displays a small banner above the player's money to indicate that
 * they are receiving a 1.5x multiplier on XP and money.  This element
 * is toggled by the client script whenever playerData.isVip changes.
 */
.vip-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #ffd700;
  margin-bottom: 4px;
  user-select: none;
}
.vip-indicator .vip-money-icon,
.vip-indicator .vip-xp-icon {
  margin-right: 3px;
}
.vip-indicator .separator {
  margin: 0 4px;
}
