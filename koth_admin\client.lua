print('[KOTH Admin] Client loading (VIP edition)...')

--[[
  This client script handles all interactions between the NUI admin
  panel and the server.  When the server instructs us to open the
  panel or update player data, we forward those messages into the
  UI.  Conversely, when the UI makes a fetch call, the fx_runtime
  turns it into a NUI callback.  We listen for those callbacks and
  convert them into appropriate server events.

  The VIP edition extends the stock admin panel by adding a new
  ``giveVip`` callback.  When the admin enters a Discord ID and
  presses the "Give VIP" button, the UI issues a POST request to
  ``giveVip``.  We receive that in this client and forward it to
  the server via a new ``koth_admin:giveVip`` event.
]]

-- Flag indicating whether the admin panel is currently open.  Used
-- to manage NUI focus.
local panelOpen = false

-- Helper to open the NUI panel and set focus
local function openPanel(data)
  panelOpen = true
  SetNuiFocus(true, true)
  SendNUIMessage({ action = 'openPanel', players = data.players, kothStatus = data.kothStatus })
end

-- Helper to update the panel data without toggling focus
local function updatePanel(data)
  SendNUIMessage({ action = 'updateData', players = data.players, kothStatus = data.kothStatus })
end

-- Helper to show a notification in the UI
local function notify(type, message)
  SendNUIMessage({ action = 'notification', type = type, message = message })
end

-- Close the admin panel and release focus
local function closePanel()
  panelOpen = false
  SetNuiFocus(false, false)
end

-- Event: Open the admin panel with initial data
RegisterNetEvent('koth_admin:openPanel')
AddEventHandler('koth_admin:openPanel', function(data)
  openPanel(data)
end)

-- Event: Update data while panel is open
RegisterNetEvent('koth_admin:updateData')
AddEventHandler('koth_admin:updateData', function(data)
  updatePanel(data)
end)

-- Event: Display a notification in the UI
RegisterNetEvent('koth_admin:notification')
AddEventHandler('koth_admin:notification', function(type, message)
  notify(type, message)
end)

-- Forward KOTH report events into the admin UI so it can render them
RegisterNetEvent('koth:newReport')
AddEventHandler('koth:newReport', function(report)
  SendNUIMessage({ action = 'newReport', report = report })
end)

RegisterNetEvent('koth:setReports')
AddEventHandler('koth:setReports', function(reports)
  SendNUIMessage({ action = 'setReports', reports = reports })
end)

-- NUI callbacks from the admin UI report tab
RegisterNUICallback('getReports', function(data, cb)
  TriggerServerEvent('koth:getReports')
  cb('ok')
end)

RegisterNUICallback('answerReport', function(data, cb)
  if data and data.id and data.response then
    TriggerServerEvent('koth:answerReport', data.id, data.response)
  end
  cb('ok')
end)

RegisterNUICallback('closeReport', function(data, cb)
  if data and data.id then
    TriggerServerEvent('koth:closeReport', data.id)
  end
  cb('ok')
end)


-- Event: Set time of day on clients (optional).  The UI may send
-- changeTimeOfDay requests; the server relays this back.  The client
-- listens here and adjusts time accordingly.
RegisterNetEvent('koth_admin:setTimeOfDay')
AddEventHandler('koth_admin:setTimeOfDay', function(time)
  if time == 'day' then
    NetworkOverrideClockTime(12, 0, 0)
  elseif time == 'night' then
    NetworkOverrideClockTime(0, 0, 0)
  end
end)

-- Close the panel if the resource stops or player leaves
AddEventHandler('onResourceStop', function(resName)
  if resName == GetCurrentResourceName() and panelOpen then
    closePanel()
  end
end)

-- NUI callback: closePanel.  Called by the UI when the user closes
-- the admin panel.  We release focus and tell the server nothing.
RegisterNUICallback('closePanel', function(data, cb)
  closePanel()
  cb('ok')
end)



-- Refresh players.  Ask the server to resend the player list and
-- KOTH status.
RegisterNUICallback('refreshPlayers', function(data, cb)
  TriggerServerEvent('koth_admin:refreshPlayers')
  cb('ok')
end)

-- Give money callback
RegisterNUICallback('giveMoney', function(data, cb)
  local playerId = tonumber(data.playerId)
  local amount = tonumber(data.amount)
  if playerId and amount then
    TriggerServerEvent('koth_admin:giveMoney', playerId, amount)
  end
  cb('ok')
end)

-- Give XP callback
RegisterNUICallback('giveXP', function(data, cb)
  local playerId = tonumber(data.playerId)
  local amount = tonumber(data.amount)
  if playerId and amount then
    TriggerServerEvent('koth_admin:giveXP', playerId, amount)
  end
  cb('ok')
end)

-- Set level callback
RegisterNUICallback('setLevel', function(data, cb)
  local playerId = tonumber(data.playerId)
  local level = tonumber(data.level)
  if playerId and level then
    TriggerServerEvent('koth_admin:setLevel', playerId, level)
  end
  cb('ok')
end)

-- Reset player stats
RegisterNUICallback('resetPlayer', function(data, cb)
  local playerId = tonumber(data.playerId)
  if playerId then
    TriggerServerEvent('koth_admin:resetPlayer', playerId)
  end
  cb('ok')
end)

-- Team change callback.  Data.team should be 'red', 'green' or 'blue'
RegisterNUICallback('forceChangeTeam', function(data, cb)
  local playerId = tonumber(data.playerId)
  local team = data.team
  if playerId and team then
    TriggerServerEvent('koth_admin:forceChangeTeam', playerId, team)
  end
  cb('ok')
end)

-- Start round callback
RegisterNUICallback('startRound', function(data, cb)
  TriggerServerEvent('koth_admin:startRound')
  cb('ok')
end)

-- Stop round callback
RegisterNUICallback('stopRound', function(data, cb)
  TriggerServerEvent('koth_admin:stopRound')
  cb('ok')
end)

-- Delete all vehicles callback
RegisterNUICallback('deleteAllVehicles', function(data, cb)
  TriggerServerEvent('koth_admin:deleteAllVehicles')
  cb('ok')
end)

-- Force next map callback
RegisterNUICallback('forceNextMap', function(data, cb)
  -- Optionally pass a winTeam if provided
  TriggerServerEvent('koth_admin:forceNextMap', data.winTeam)
  cb('ok')
end)

-- Change time of day callback
RegisterNUICallback('changeTimeOfDay', function(data, cb)
  local time = data.time
  if time then
    TriggerServerEvent('koth_admin:changeTimeOfDay', time)
  end
  cb('ok')
end)

-- Set class XP callback
RegisterNUICallback('setClassXP', function(data, cb)
  local playerId = tonumber(data.playerId)
  local classId = data.classId
  local xpAmount = tonumber(data.xpAmount)
  if playerId and classId and xpAmount then
    TriggerServerEvent('koth_admin:setClassXP', playerId, classId, xpAmount)
  end
  cb('ok')
end)

-- Reset prestige callback
RegisterNUICallback('resetPrestige', function(data, cb)
  local playerId = tonumber(data.playerId)
  if playerId then
    TriggerServerEvent('koth_admin:resetPrestige', playerId)
  end
  cb('ok')
end)

-- Give VIP callback.  Accepts a Discord ID (with or without prefix)
RegisterNUICallback('giveVip', function(data, cb)
  local discordId = data.discordId
  if discordId and discordId ~= '' then
    TriggerServerEvent('koth_admin:giveVip', tostring(discordId))
  end
  cb('ok')
end)

-- Fallback callback: any unknown callback returns ok to prevent
-- errors.  This can be useful if the UI calls an endpoint we have
-- not yet implemented; adjust as needed.
RegisterNUICallback('__cfx_nui:unknown', function(data, cb)
  cb('ok')
end)