// VIP Weapon Skin Changer - JavaScript
// Handles UI interactions, drag and drop, and communication with client

let currentWeaponData = null;
let selectedTint = 0;
let isApplying = false;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('[VIP Skins] UI loaded and DOM ready');

    // Setup event listeners
    setupEventListeners();

    // Hide UI initially
    const skinChanger = document.getElementById('skinChanger');
    if (skinChanger) {
        skinChanger.style.display = 'none';
        console.log('[VIP Skins] UI container found and hidden');
    } else {
        console.error('[VIP Skins] ERROR: Could not find skinChanger element!');
    }

    // Test message to confirm <PERSON><PERSON> is working
    console.log('[VIP Skins] NUI system initialized successfully');
});

// Setup all event listeners
function setupEventListeners() {
    // Close button
    document.getElementById('closeBtn').addEventListener('click', closeSkinChanger);
    
    // Cancel button
    document.getElementById('cancelBtn').addEventListener('click', closeSkinChanger);
    
    // Apply button
    document.getElementById('applyBtn').addEventListener('click', applySkin);
    
    // Reset preview button
    document.getElementById('resetPreview').addEventListener('click', resetPreview);
    
    // ESC key to close
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.getElementById('skinChanger').style.display !== 'none') {
            closeSkinChanger();
        }
    });
}

// Listen for messages from client
window.addEventListener('message', function(event) {
    console.log('[VIP Skins] Received message from client:', event.data);
    const data = event.data;

    if (!data || !data.type) {
        console.warn('[VIP Skins] Invalid message data received');
        return;
    }

    switch(data.type) {
        case 'openSkinChanger':
            console.log('[VIP Skins] Processing openSkinChanger message');
            openSkinChanger(data.data);
            break;
        case 'closeSkinChanger':
            console.log('[VIP Skins] Processing closeSkinChanger message');
            closeSkinChanger();
            break;
        default:
            console.warn('[VIP Skins] Unknown message type:', data.type);
            break;
    }
});

// Open the skin changer with weapon data
function openSkinChanger(data) {
    console.log('[VIP Skins] Opening skin changer with data:', data);

    try {
        if (!data) {
            console.error('[VIP Skins] No data provided to openSkinChanger');
            return;
        }

        currentWeaponData = data;
        selectedTint = data.savedSkin ? data.savedSkin.tint : 0;

        console.log('[VIP Skins] Setting selectedTint to:', selectedTint);

        // Update weapon info
        console.log('[VIP Skins] Updating weapon info...');
        updateWeaponInfo(data.weapon);

        // Populate skin options
        console.log('[VIP Skins] Populating skin options...');
        populateSkinOptions(data.tints, data.savedSkin);

        // Show the UI with animation
        console.log('[VIP Skins] Showing UI container...');
        const container = document.getElementById('skinChanger');
        if (container) {
            container.style.display = 'block';
            console.log('[VIP Skins] UI container is now visible');

            // Focus on the container for keyboard events
            container.focus();
        } else {
            console.error('[VIP Skins] ERROR: skinChanger container not found!');
            return;
        }

        // Play open sound (if available)
        playSound('open');

        console.log('[VIP Skins] Skin changer opened successfully');

    } catch (error) {
        console.error('[VIP Skins] Error opening skin changer:', error);
    }
}

// Update weapon information display
function updateWeaponInfo(weapon) {
    document.getElementById('weaponName').textContent = weapon.name || 'Unknown Weapon';
    document.getElementById('weaponHash').textContent = `Hash: 0x${weapon.hash.toString(16).toUpperCase()}`;
    
    // Update current skin name
    const currentTint = currentWeaponData.savedSkin ? currentWeaponData.savedSkin.tint : 0;
    const currentSkinName = currentWeaponData.tints[currentTint] ? currentWeaponData.tints[currentTint].name : 'Default';
    document.getElementById('currentSkinName').textContent = currentSkinName;
}

// Populate skin options grid
function populateSkinOptions(tints, savedSkin) {
    const skinGrid = document.getElementById('skinGrid');
    skinGrid.innerHTML = '';
    
    // Create skin option for each tint
    Object.keys(tints).forEach(tintId => {
        const tint = tints[tintId];
        const isSelected = savedSkin && savedSkin.tint == tintId;
        
        const skinOption = createSkinOption(tintId, tint, isSelected);
        skinGrid.appendChild(skinOption);
    });
}

// Create a skin option element
function createSkinOption(tintId, tint, isSelected = false) {
    const option = document.createElement('div');
    option.className = `skin-option ${isSelected ? 'selected' : ''}`;
    option.dataset.tintId = tintId;
    
    // Create color preview
    const colorDiv = document.createElement('div');
    colorDiv.className = 'skin-color';
    colorDiv.style.backgroundColor = tint.color;
    
    // Create name label
    const nameDiv = document.createElement('div');
    nameDiv.className = 'skin-name';
    nameDiv.textContent = tint.name;
    
    option.appendChild(colorDiv);
    option.appendChild(nameDiv);
    
    // Add click event
    option.addEventListener('click', function() {
        selectSkinOption(tintId, option);
    });
    
    // Add drag and drop functionality
    setupDragAndDrop(option, tintId);
    
    return option;
}

// Setup drag and drop for skin options
function setupDragAndDrop(element, tintId) {
    element.draggable = true;
    
    element.addEventListener('dragstart', function(e) {
        e.dataTransfer.setData('text/plain', tintId);
        element.style.opacity = '0.5';
        playSound('drag');
    });
    
    element.addEventListener('dragend', function(e) {
        element.style.opacity = '1';
    });
    
    // Make preview area a drop zone
    const previewContainer = document.querySelector('.preview-container');
    
    previewContainer.addEventListener('dragover', function(e) {
        e.preventDefault();
        previewContainer.style.backgroundColor = 'rgba(255, 215, 0, 0.1)';
    });
    
    previewContainer.addEventListener('dragleave', function(e) {
        previewContainer.style.backgroundColor = '';
    });
    
    previewContainer.addEventListener('drop', function(e) {
        e.preventDefault();
        const droppedTintId = e.dataTransfer.getData('text/plain');
        previewContainer.style.backgroundColor = '';
        
        // Apply the dropped skin
        selectSkinOption(droppedTintId);
        playSound('drop');
        
        // Show cool drop effect
        showDropEffect(e.clientX, e.clientY);
    });
}

// Select a skin option
function selectSkinOption(tintId, optionElement = null) {
    selectedTint = parseInt(tintId);
    
    // Update UI selection
    document.querySelectorAll('.skin-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    if (optionElement) {
        optionElement.classList.add('selected');
    } else {
        // Find and select the option by tint ID
        const option = document.querySelector(`[data-tint-id="${tintId}"]`);
        if (option) {
            option.classList.add('selected');
        }
    }
    
    // Update current skin display
    const tintName = currentWeaponData.tints[tintId] ? currentWeaponData.tints[tintId].name : 'Unknown';
    document.getElementById('currentSkinName').textContent = tintName;
    
    // Send preview request to client
    fetch(`https://${GetParentResourceName()}/previewTint`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tintId: tintId
        })
    });
    
    // Play selection sound
    playSound('select');
    
    // Add visual feedback
    addSelectionEffect(optionElement);
}

// Add visual selection effect
function addSelectionEffect(element) {
    if (!element) return;
    
    element.style.transform = 'scale(1.1)';
    element.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.6)';
    
    setTimeout(() => {
        element.style.transform = '';
        element.style.boxShadow = '';
    }, 300);
}

// Show drop effect animation
function showDropEffect(x, y) {
    const effect = document.createElement('div');
    effect.style.position = 'fixed';
    effect.style.left = x + 'px';
    effect.style.top = y + 'px';
    effect.style.width = '20px';
    effect.style.height = '20px';
    effect.style.backgroundColor = '#ffd700';
    effect.style.borderRadius = '50%';
    effect.style.pointerEvents = 'none';
    effect.style.zIndex = '9999';
    effect.style.animation = 'dropEffect 0.6s ease-out forwards';
    
    document.body.appendChild(effect);
    
    setTimeout(() => {
        document.body.removeChild(effect);
    }, 600);
}

// Apply the selected skin
function applySkin() {
    if (isApplying) return;
    
    isApplying = true;
    
    // Show loading overlay
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // Play apply sound
    playSound('apply');
    
    // Send apply request to client
    fetch(`https://${GetParentResourceName()}/applySkin`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tintId: selectedTint
        })
    }).then(() => {
        // Hide loading after a short delay for effect
        setTimeout(() => {
            document.getElementById('loadingOverlay').style.display = 'none';
            showNotification('Skin applied successfully!');
            isApplying = false;
        }, 1000);
    }).catch(() => {
        document.getElementById('loadingOverlay').style.display = 'none';
        showNotification('Failed to apply skin!', 'error');
        isApplying = false;
    });
}

// Reset preview
function resetPreview() {
    // Reset to saved skin or default
    const savedTint = currentWeaponData.savedSkin ? currentWeaponData.savedSkin.tint : 0;
    selectSkinOption(savedTint);
    
    playSound('reset');
}

// Close the skin changer
function closeSkinChanger() {
    const container = document.getElementById('skinChanger');
    
    // Add closing animation
    container.style.animation = 'slideOut 0.3s ease-in forwards';
    
    setTimeout(() => {
        container.style.display = 'none';
        container.style.animation = '';
        
        // Send close message to client
        fetch(`https://${GetParentResourceName()}/closeSkinChanger`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });
    }, 300);
    
    playSound('close');
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notificationText');
    
    notificationText.textContent = message;
    
    if (type === 'error') {
        notification.style.background = 'linear-gradient(45deg, #ff4757, #ff6b7a)';
    } else {
        notification.style.background = 'linear-gradient(45deg, #2ed573, #7bed9f)';
    }
    
    notification.style.display = 'block';
    
    setTimeout(() => {
        notification.style.display = 'none';
    }, 3000);
}

// Play sound effects
function playSound(soundName) {
    try {
        const audio = new Audio(`sounds/${soundName}.ogg`);
        audio.volume = 0.3;
        audio.play().catch(() => {
            // Ignore audio errors
        });
    } catch (e) {
        // Ignore audio errors
    }
}

// Get parent resource name for fetch requests
function GetParentResourceName() {
    return window.location.hostname;
}

// Add CSS animation for slide out and drop effects
const style = document.createElement('style');
style.textContent = `
    @keyframes dropEffect {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        100% {
            transform: scale(3);
            opacity: 0;
        }
    }

    @keyframes slideOut {
        from {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
        to {
            opacity: 0;
            transform: translate(-50%, -60%) scale(0.9);
        }
    }

    .skin-option:active {
        transform: scale(0.95);
    }

    .preview-container.drag-over {
        background-color: rgba(255, 215, 0, 0.2) !important;
        border: 2px dashed #ffd700;
        border-radius: 10px;
    }
`;
document.head.appendChild(style);

// Enhanced drag and drop with better visual feedback
function enhanceDragAndDrop() {
    const previewContainer = document.querySelector('.preview-container');

    // Add better drag over effects
    previewContainer.addEventListener('dragenter', function(e) {
        e.preventDefault();
        this.classList.add('drag-over');
    });

    previewContainer.addEventListener('dragleave', function(e) {
        // Only remove if we're actually leaving the container
        if (!this.contains(e.relatedTarget)) {
            this.classList.remove('drag-over');
        }
    });

    previewContainer.addEventListener('drop', function(e) {
        this.classList.remove('drag-over');
    });
}

// Call enhanced drag and drop setup
document.addEventListener('DOMContentLoaded', function() {
    enhanceDragAndDrop();
});
