MAPS = MAPS or {}
MAPS['university'] = {
    friendlyName = "University",
    Spawns = {
        green = { coords = vector3(80.466,255.02,108.794), heading = 337.323, radius = 200.0 },
        blue = { coords = vector3(-815.84, -1092.73, 10.93), radius = 200.0 }
    },
    Hill = { coords = vector3(-1657.813, 170.811, 81.159), radius = 300.0 },
    keyPoints = {
        vector3(-1521.72, 121.02, 75.26),
        vector3(-1461.06, 189.45, 62.39),
        vector3(-1591.31, -34.89, 77.4),
        vector3(-1474.88, 25.35, 70.78)
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(86.268,258.738,108.828), heading = 113.386,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(78.844,262.484,109.081), heading = 158.74, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(98.571,231.323,108.339), heading = 340.157, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(115.952,225.138,107.699), heading = 342.992, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(98.570,256.826,108.474), heading = 158.74, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(71.934,268.286,109.923), heading = 195.591, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(2529.16, 2630.62, 37.78), heading = 290.15 },
        Spawnpoints = {
            Cars = { coords = vector3(53.301,254.98,109.131), heading = 70.866 },
            Helicopters = { coords = vector3(74.927,237.943,109.519), heading = 68.031 }
        },
        RespawnVehicle = {coords = vector3(78.712,248.044,109.131), heading = 68.031},
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(-806.53, -1091.77, 10.88), heading = 93.92,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(-810.82, -1084.49, 11.08), heading = 178.43, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(-793.07, -1143.02, 10.33), heading = 27.139, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-823.51, -1165.58, 7.38), heading = 31.31, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-811.43, -1099.71, 10.76), heading = 25.51, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(-822.4, -1095.59, 11.13), heading = 300.47, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(-811.59, -1082.5, 10.64), heading = 254.7 },
        Spawnpoints = {
            Cars = { coords = vector3(-783.798,-1099.912,10.172), heading = 28.346 },
            Helicopters = { coords = vector3(-782.624,-1080.25,11.385), heading = 25.512 },
        },
        RespawnVehicle = {coords = vector3(-797.314,-1081.661,11.183), heading = 28.346},
    }
}