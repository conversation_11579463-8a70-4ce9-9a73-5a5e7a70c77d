-- KILL SYSTEM TEST COMMANDS
-- This file contains test commands to help diagnose kill system issues

--[[
  Test command to simulate the full kill flow for debugging.  Disabled for
  production to prevent exposing internal mechanics to players.
]]
-- RegisterCommand('testkillflow', function(source, args, rawCommand)
  if source == 0 then
    print('[KOTH TEST] This command must be used by a player')
    return
  end
  
  local testVictim = tonumber(args[1])
  if not testVictim then
    print('[KOTH TEST] Usage: /testkillflow [victim player id]')
    TriggerClientEvent('chat:addMessage', source, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Usage: /testkillflow [victim player id]"}
    })
    return
  end
  
  local inZone = args[2] == 'zone' or args[2] == 'true'
  
  print('[KOTH TEST] ===== KILL FLOW TEST START =====')
  print('[KOTH TEST] Killer:', source, 'Victim:', testVictim, 'In Zone:', inZone)
  
  -- Trigger the kill event
  TriggerEvent('koth:playerKilled', source, testVictim, inZone)
  
  print('[KOTH TEST] ===== KILL FLOW TEST END =====')
-- end, false)

--[[
  Test command to trigger the kill reward UI only.  Disabled for production.
]]
-- RegisterCommand('testkillui', function(source, args, rawCommand)
  if source == 0 then
    print('[KOTH TEST] This command must be used by a player')
    return
  end
  
  local inZone = args[1] == 'zone' or args[1] == 'true'
  local xp = inZone and 150 or 50
  local money = inZone and 150 or 50
  
  print('[KOTH TEST] Testing kill UI for player', source)
  
  -- Send kill reward UI
  TriggerClientEvent('koth:showKillReward', source, {
    xp = xp,
    money = money,
    inZone = inZone,
    victimName = 'Test Victim',
    killerTeam = 'red',
    victimTeam = 'blue'
  })
  
  print('[KOTH TEST] Kill UI test sent')
-- end, false)

--[[
  Test command to check player data integrity.  Disabled for production.
]]
-- RegisterCommand('checkplayerdata', function(source, args, rawCommand)
  if source == 0 then
    print('[KOTH TEST] Checking all player data:')
    for playerId, data in pairs(playerData or {}) do
      print(string.format('  Player %d: %s', playerId, json.encode(data)))
    end
  else
    local targetId = tonumber(args[1]) or source
    if playerData and playerData[targetId] then
      print('[KOTH TEST] Player', targetId, 'data:', json.encode(playerData[targetId]))
      TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"[KOTH]", "Player " .. targetId .. " data: " .. json.encode(playerData[targetId])}
      })
    else
      print('[KOTH TEST] No data found for player', targetId)
      TriggerClientEvent('chat:addMessage', source, {
        color = {255, 0, 0},
        multiline = true,
        args = {"[KOTH]", "No data found for player " .. targetId}
      })
    end
  end
-- end, false)

--[[
  Test command to force load a player's data.  Disabled for production.
]]
-- RegisterCommand('forceloaddata', function(source, args, rawCommand)
  local targetId = source
  if source == 0 then
    targetId = tonumber(args[1])
    if not targetId then
      print('[KOTH TEST] Usage: forceloaddata [player id]')
      return
    end
  end
  
  print('[KOTH TEST] Force loading data for player', targetId)
  LoadPlayerData(targetId)
  
  Citizen.SetTimeout(2000, function()
    if playerData and playerData[targetId] then
      print('[KOTH TEST] Data loaded successfully:', json.encode(playerData[targetId]))
    else
      print('[KOTH TEST] Failed to load data for player', targetId)
    end
  end)
-- end, false)

--[[
  Test command to set a player's team.  Disabled for production.
]]
-- RegisterCommand('setteam', function(source, args, rawCommand)
  if source == 0 then
    print('[KOTH TEST] This command must be used by a player')
    return
  end
  
  local team = args[1]
  if not team or (team ~= 'red' and team ~= 'blue' and team ~= 'green') then
    TriggerClientEvent('chat:addMessage', source, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Usage: /setteam [red/blue/green]"}
    })
    return
  end
  
  playerTeams[source] = team
  print('[KOTH TEST] Set player', source, 'to team', team)
  
  TriggerClientEvent('chat:addMessage', source, {
    color = {0, 255, 0},
    multiline = true,
    args = {"[KOTH]", "You have been assigned to " .. team .. " team"}
  })
  
  UpdateTeamCounts()
-- end, false)

--[[
  Test command to list all team assignments.  Disabled for production.
]]
-- RegisterCommand('checkteams', function(source, args, rawCommand)
  print('[KOTH TEST] Current team assignments:')
  for playerId, team in pairs(playerTeams or {}) do
    local playerName = GetPlayerName(playerId) or 'Unknown'
    print(string.format('  Player %d (%s): %s team', playerId, playerName, team))
  end
  
  if source ~= 0 then
    TriggerClientEvent('chat:addMessage', source, {
      color = {0, 255, 0},
      multiline = true,
      args = {"[KOTH]", "Check server console for team assignments"}
    })
  end
-- end, false)

--[[
  Informational prints for kill system testing.  Disabled for production.
]]
-- print('[KOTH TEST] Kill system test commands loaded')
-- print('[KOTH TEST] Available commands:')
-- print('  /testkillflow [victim id] [zone] - Test full kill flow')
-- print('  /testkillui [zone] - Test kill UI only')
-- print('  /checkplayerdata [player id] - Check player data')
-- print('  /forceloaddata [player id] - Force load player data')
-- print('  /setteam [red/blue/green] - Set your team')
-- print('  /checkteams - Check all team assignments')
