-- VIP Weapon Skin Changer - Client Side
-- Handles weapon detection, 3D preview, and UI interaction

local RESOURCE_NAME = GetCurrentResourceName()
local isUIOpen = false
local currentWeapon = nil
local currentWeaponHash = nil
local previewObject = nil
local savedSkins = {}

-- Weapon tint/skin data with enhanced descriptions
local weaponTints = {
    [0] = {name = "Default", color = "#FFFFFF", description = "Standard factory finish"},
    [1] = {name = "Green", color = "#4CAF50", description = "Military woodland camo"},
    [2] = {name = "Gold", color = "#FFD700", description = "Luxury gold plating"},
    [3] = {name = "Pink", color = "#FF69B4", description = "Vibrant pink finish"},
    [4] = {name = "Army", color = "#8B4513", description = "Desert combat camo"},
    [5] = {name = "LSPD", color = "#0066CC", description = "Police department blue"},
    [6] = {name = "Orange", color = "#FF8C00", description = "High-visibility orange"},
    [7] = {name = "Platinum", color = "#E5E4E2", description = "Premium platinum coating"}
}

-- Special weapon categories for enhanced effects
local weaponCategories = {
    pistols = {
        "WEAPON_PISTOL", "WEAPON_COMBATPISTOL", "WEAPON_APPISTOL",
        "WEAPON_PISTOL50", "WEAPON_REVOLVER", "WEAPON_REVOLVER_MK2"
    },
    rifles = {
        "WEAPON_CARBINERIFLE", "WEAPON_ADVANCEDRIFLE", "WEAPON_BULLPUPRIFLE",
        "WEAPON_MILITARYRIFLE", "WEAPON_TACTICALRIFLE", "WEAPON_SERVICERIFLE"
    },
    smgs = {
        "WEAPON_SMG", "WEAPON_MICROSMG", "WEAPON_ASSAULTSMG", "WEAPON_COMBATPDW"
    }
}

-- Get weapon category for special effects
local function GetWeaponCategory(weaponHash)
    local weaponName = GetWeaponDisplayNameFromHash(weaponHash)

    for category, weapons in pairs(weaponCategories) do
        for _, weapon in ipairs(weapons) do
            if GetHashKey(weapon) == weaponHash then
                return category
            end
        end
    end

    return "other"
}

-- Initialize the resource
Citizen.CreateThread(function()
    print(('[%s] Client loaded successfully'):format(RESOURCE_NAME))
    
    -- Load saved skin preferences
    TriggerServerEvent('koth_vip_skins:load_preferences')
end)

-- Function to get current weapon
local function GetCurrentWeaponInfo()
    local playerPed = PlayerPedId()
    local weaponHash = GetSelectedPedWeapon(playerPed)
    
    if weaponHash and weaponHash ~= GetHashKey('WEAPON_UNARMED') then
        local weaponName = GetWeaponDisplayNameFromHash(weaponHash)
        return {
            hash = weaponHash,
            name = weaponName,
            model = GetWeapontypeModel(weaponHash)
        }
    end
    
    return nil
end

-- Function to create 3D weapon preview
local function CreateWeaponPreview(weaponHash)
    if previewObject then
        DeleteObject(previewObject)
        previewObject = nil
    end
    
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    -- Create the weapon object for preview
    local weaponModel = GetWeapontypeModel(weaponHash)
    
    RequestModel(weaponModel)
    while not HasModelLoaded(weaponModel) do
        Citizen.Wait(1)
    end
    
    -- Create object slightly in front of player
    local forwardVector = GetEntityForwardVector(playerPed)
    local previewCoords = vector3(
        playerCoords.x + forwardVector.x * 2.0,
        playerCoords.y + forwardVector.y * 2.0,
        playerCoords.z + 1.0
    )
    
    previewObject = CreateObject(weaponModel, previewCoords.x, previewCoords.y, previewCoords.z, false, false, false)
    
    if previewObject then
        SetEntityCollision(previewObject, false, false)
        FreezeEntityPosition(previewObject, true)
        SetEntityAlpha(previewObject, 200, false)
        
        -- Start rotation animation
        Citizen.CreateThread(function()
            while previewObject and DoesEntityExist(previewObject) and isUIOpen do
                local currentRotation = GetEntityRotation(previewObject, 2)
                SetEntityRotation(previewObject, currentRotation.x, currentRotation.y, currentRotation.z + 1.0, 2, true)
                Citizen.Wait(50)
            end
        end)
        
        print(('[%s] Created weapon preview object'):format(RESOURCE_NAME))
        return true
    end
    
    return false
end

-- Function to apply tint to preview object
local function ApplyTintToPreview(tintId)
    if previewObject and DoesEntityExist(previewObject) then
        -- Note: SetObjectTintIndex might not work on all weapon models
        -- This is a limitation of GTA V's weapon system
        SetObjectTintIndex(previewObject, tintId)
        
        -- Alternative: Change object color/material
        if tintId > 0 then
            SetEntityAlpha(previewObject, 180, false)
        else
            SetEntityAlpha(previewObject, 200, false)
        end
    end
end

-- Function to cleanup preview
local function CleanupPreview()
    if previewObject then
        DeleteObject(previewObject)
        previewObject = nil
    end
end

-- Open skin changer UI
RegisterNetEvent('koth_vip_skins:open_skin_changer')
AddEventHandler('koth_vip_skins:open_skin_changer', function()
    print(('[%s] [DEBUG] Received open_skin_changer event'):format(RESOURCE_NAME))

    local weaponInfo = GetCurrentWeaponInfo()
    print(('[%s] [DEBUG] Current weapon info: %s'):format(RESOURCE_NAME, json.encode(weaponInfo)))

    if not weaponInfo then
        print(('[%s] [DEBUG] No weapon found, showing error'):format(RESOURCE_NAME))
        -- Show stylized notification that player needs to hold a weapon
        ShowAdvancedNotification(
            "VIP SKIN CHANGER",
            "~r~Error",
            "You need to hold a weapon to use the skin changer!",
            "CHAR_AMMUNATION",
            1
        )

        -- Play error sound
        PlaySoundFrontend(-1, "ERROR", "HUD_FRONTEND_DEFAULT_SOUNDSET", true)
        return
    end

    print(('[%s] [DEBUG] Setting up UI for weapon: %s (Hash: %s)'):format(RESOURCE_NAME, weaponInfo.name, weaponInfo.hash))

    currentWeapon = weaponInfo
    currentWeaponHash = weaponInfo.hash
    isUIOpen = true

    -- Play opening sound
    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", true)

    -- Create 3D preview with enhanced effects
    print(('[%s] [DEBUG] Creating weapon preview'):format(RESOURCE_NAME))
    CreateWeaponPreview(weaponInfo.hash)

    -- Prepare data for UI
    local uiData = {
        weapon = weaponInfo,
        tints = weaponTints,
        savedSkin = savedSkins[tostring(weaponInfo.hash)] or {tint = 0}
    }

    print(('[%s] [DEBUG] UI Data prepared: %s'):format(RESOURCE_NAME, json.encode(uiData)))

    -- Open UI with fade effect
    print(('[%s] [DEBUG] Starting screen fade and NUI setup'):format(RESOURCE_NAME))
    DoScreenFadeOut(200)
    Citizen.Wait(200)

    print(('[%s] [DEBUG] Setting NUI focus and sending message'):format(RESOURCE_NAME))
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openSkinChanger',
        data = uiData
    })

    DoScreenFadeIn(300)

    -- Show opening notification
    ShowAdvancedNotification(
        "VIP SKIN CHANGER",
        "~y~Opened",
        "Welcome to the VIP Weapon Skin Changer!",
        "CHAR_AMMUNATION",
        1
    )

    print(('[%s] [DEBUG] UI should now be open for weapon: %s'):format(RESOURCE_NAME, weaponInfo.name))
end)

-- Enhanced notification function
function ShowAdvancedNotification(title, subtitle, message, icon, iconType)
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName(message)
    EndTextCommandThefeedPostMessagetext(icon, icon, false, iconType, title, subtitle)
    EndTextCommandThefeedPostTicker(false, true)
end

-- Handle UI callbacks
RegisterNUICallback('closeSkinChanger', function(data, cb)
    isUIOpen = false
    SetNuiFocus(false, false)
    CleanupPreview()
    
    cb('ok')
end)

RegisterNUICallback('previewTint', function(data, cb)
    local tintId = tonumber(data.tintId) or 0
    
    -- Apply tint to preview
    ApplyTintToPreview(tintId)
    
    -- Apply tint to player's current weapon for live preview
    local playerPed = PlayerPedId()
    if HasPedGotWeapon(playerPed, currentWeaponHash, false) then
        SetPedWeaponTintIndex(playerPed, currentWeaponHash, tintId)
    end
    
    cb('ok')
end)

RegisterNUICallback('applySkin', function(data, cb)
    local tintId = tonumber(data.tintId) or 0
    
    -- Send to server to save and apply
    TriggerServerEvent('koth_vip_skins:apply_skin', currentWeaponHash, {
        tint = tintId
    })
    
    -- Close UI
    isUIOpen = false
    SetNuiFocus(false, false)
    CleanupPreview()
    
    -- Show success notification
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName(string.format("Applied %s tint to your weapon!", weaponTints[tintId].name))
    EndTextCommandThefeedPostTicker(false, true)
    
    cb('ok')
end)

-- Apply weapon skin from server
RegisterNetEvent('koth_vip_skins:apply_weapon_skin')
AddEventHandler('koth_vip_skins:apply_weapon_skin', function(weaponHash, skinData)
    local playerPed = PlayerPedId()

    if HasPedGotWeapon(playerPed, weaponHash, false) then
        -- Apply the skin with a cool effect
        SetPedWeaponTintIndex(playerPed, weaponHash, skinData.tint or 0)

        -- Play a cool sound effect
        PlaySoundFrontend(-1, "WEAPON_PURCHASE", "HUD_AMMO_SHOP_SOUNDSET", true)

        -- Create a visual effect around the player
        CreateSkinApplicationEffect(playerPed)

        print(('[%s] Applied skin to weapon %s with tint %d'):format(RESOURCE_NAME, weaponHash, skinData.tint or 0))
    end
end)

-- Create visual effect when applying skin
function CreateSkinApplicationEffect(playerPed)
    local playerCoords = GetEntityCoords(playerPed)

    -- Create particle effect (if available)
    Citizen.CreateThread(function()
        RequestNamedPtfxAsset("scr_rcbarry2")
        while not HasNamedPtfxAssetLoaded("scr_rcbarry2") do
            Citizen.Wait(1)
        end

        -- Create a golden sparkle effect
        UseParticleFxAssetNextCall("scr_rcbarry2")
        local effect = StartParticleFxLoopedAtCoord("scr_clown_appears", playerCoords.x, playerCoords.y, playerCoords.z + 1.0, 0.0, 0.0, 0.0, 0.5, false, false, false, false)

        -- Stop the effect after 2 seconds
        Citizen.Wait(2000)
        StopParticleFxLooped(effect, 0)
        RemoveNamedPtfxAsset("scr_rcbarry2")
    end)

    -- Screen flash effect
    Citizen.CreateThread(function()
        local startTime = GetGameTimer()
        local duration = 500 -- 0.5 seconds

        while GetGameTimer() - startTime < duration do
            local progress = (GetGameTimer() - startTime) / duration
            local alpha = math.floor((1.0 - progress) * 50) -- Fade from 50 to 0

            DrawRect(0.5, 0.5, 1.0, 1.0, 255, 215, 0, alpha) -- Golden flash
            Citizen.Wait(0)
        end
    end)
end

-- Load saved skins
RegisterNetEvent('koth_vip_skins:load_saved_skins')
AddEventHandler('koth_vip_skins:load_saved_skins', function(skins)
    savedSkins = {}
    
    for _, skin in ipairs(skins) do
        savedSkins[tostring(skin.weapon_hash)] = {
            tint = skin.tint_id
        }
        
        -- Apply saved skin if player currently has this weapon
        local playerPed = PlayerPedId()
        if HasPedGotWeapon(playerPed, skin.weapon_hash, false) then
            SetPedWeaponTintIndex(playerPed, skin.weapon_hash, skin.tint_id)
        end
    end
    
    print(('[%s] Loaded %d saved weapon skins'):format(RESOURCE_NAME, #skins))
end)

-- Auto-apply saved skins when weapons are given
Citizen.CreateThread(function()
    local lastWeapon = nil
    
    while true do
        Citizen.Wait(1000) -- Check every second
        
        local currentWeaponHash = GetSelectedPedWeapon(PlayerPedId())
        
        if currentWeaponHash ~= lastWeapon and currentWeaponHash ~= GetHashKey('WEAPON_UNARMED') then
            lastWeapon = currentWeaponHash
            
            -- Check if we have a saved skin for this weapon
            local savedSkin = savedSkins[tostring(currentWeaponHash)]
            if savedSkin then
                SetPedWeaponTintIndex(PlayerPedId(), currentWeaponHash, savedSkin.tint)
            end
        end
    end
end)

-- Debug command to test UI without weapon requirement
RegisterCommand('testskinui', function(source, args, rawCommand)
    print(('[%s] [DEBUG] Test UI command triggered'):format(RESOURCE_NAME))

    -- Create fake weapon data for testing
    local fakeWeaponInfo = {
        hash = GetHashKey('WEAPON_PISTOL'),
        name = 'Pistol (Test)',
        model = GetWeapontypeModel(GetHashKey('WEAPON_PISTOL'))
    }

    currentWeapon = fakeWeaponInfo
    currentWeaponHash = fakeWeaponInfo.hash
    isUIOpen = true

    -- Prepare data for UI
    local uiData = {
        weapon = fakeWeaponInfo,
        tints = weaponTints,
        savedSkin = {tint = 0}
    }

    print(('[%s] [DEBUG] Test UI Data: %s'):format(RESOURCE_NAME, json.encode(uiData)))

    -- Open UI directly without fade effects for testing
    print(('[%s] [DEBUG] Setting NUI focus and sending test message'):format(RESOURCE_NAME))
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openSkinChanger',
        data = uiData
    })

    print(('[%s] [DEBUG] Test UI should now be open'):format(RESOURCE_NAME))
end, false)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == RESOURCE_NAME then
        CleanupPreview()
        if isUIOpen then
            SetNuiFocus(false, false)
        end
    end
end)
