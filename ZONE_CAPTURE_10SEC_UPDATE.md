# Zone Capture System - 10 Second Update

## Changes Made:

### 1. **Server-side (server.lua)**
- Changed `captureThreshold` from 100.0 to 10.0
- This means zones now only take 10 seconds to capture instead of 100 seconds
- Points are awarded every 10 seconds of uncontested control

### 2. **Client-side (script_koth_fix.js)**
- Updated the UI to properly handle the 10-second threshold
- Progress bar now correctly shows 0-100% over 10 seconds

### 3. **Point Persistence**
- Zone points are already persistent by design
- Each team's points are stored in the `zonePoints` table
- Points never reset - they only increase when a team controls the zone
- If Red team has 15 points and dies, their 15 points remain
- When Blue team takes control, they start earning their own points
- All teams keep their accumulated points throughout the game

## How It Works Now:

1. **Zone Capture (10 seconds)**
   - When a single team enters an empty zone, it instantly becomes their color
   - Progress bar starts at 0% and fills to 100% over 10 seconds
   - After 10 seconds, the team gets 1 point
   - Progress resets to 0% and starts counting again for the next point

2. **Point System**
   - Red team captures zone → earns points every 10 seconds
   - Red team dies/leaves → Red keeps all their points
   - Blue team captures zone → Blue starts earning their own points
   - Red still has their points, Blue accumulates their own
   - Points are displayed in the colored boxes at the top of the HUD

3. **Contested Zones**
   - When multiple teams are in the zone, it becomes "CONTESTED"
   - No points are awarded while contested
   - Progress stops until only one team remains

## Testing:
1. Join Red team and capture the zone for 30 seconds (should get 3 points)
2. Leave the zone or die
3. Join Blue team and capture the zone
4. Red team should still have their 3 points
5. Blue team starts earning their own points every 10 seconds

The zone system now works with 10-second captures and persistent team points as requested!
