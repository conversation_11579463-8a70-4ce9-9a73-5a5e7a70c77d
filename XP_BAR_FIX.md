# XP Bar Update Fix

## Issue
The XP bar on the HUD was not updating after kills, even though the kill rewards were being processed and money was updating correctly.

## Root Cause
The client-side `koth:showKillR<PERSON>ard` event handler was updating the local `playerStats.xp` value but NOT recalculating the player's level. Since the XP bar display logic in the UI depends on both the XP value AND the current level to calculate progress, the bar wasn't updating correctly because it was using an outdated level value.

## Solution
Added level recalculation logic to the `koth:showKillReward` event handler in `client.lua`:

```lua
-- Recalculate level based on new XP
local function CalculateLevel(xp)
  local levels = {
    {level = 1, required = 0},
    {level = 2, required = 100},
    {level = 3, required = 250},
    {level = 4, required = 500},
    {level = 5, required = 1000},
    {level = 6, required = 1750},
    {level = 7, required = 2750},
    {level = 8, required = 4000},
    {level = 9, required = 6000},
    {level = 10, required = 8500}
  }
  local currentLevel = 1
  for _, levelData in ipairs(levels) do
    if xp >= levelData.required then
      currentLevel = levelData.level
    else
      break
    end
  end
  return currentLevel
end

local newLevel = CalculateLevel(playerStats.xp)
if newLevel ~= playerStats.level then
  print('[KOTH] Player level updated from ' .. tostring(playerStats.level) .. ' to ' .. tostring(newLevel))
  playerStats.level = newLevel
end
```

## How It Works
1. When a kill reward is received, the client updates the local XP value
2. The new `CalculateLevel` function determines what level the player should be at with their new XP total
3. If the level has changed, it updates `playerStats.level` 
4. The updated level is then sent to the UI along with the XP, allowing the XP bar to calculate the correct progress percentage

## Testing
1. Get a kill and verify the XP bar updates immediately
2. Get multiple kills to test level progression
3. Use `/testkillreward` or `/testkillreward zone` to test without actual kills

## Note
The server still maintains the authoritative player data and will sync the correct values periodically. This client-side calculation ensures immediate visual feedback for a better user experience.
