# XP Bar Debug Guide

## Testing Steps

### 1. Check Current Player Data
First, check what data the server has for you:
```
/checkstats
```
This will show your current Money, XP, Level, Kills, and Deaths.

### 2. Test Kill Rewards
Test the kill reward system to see if XP is being added:
```
/testkill        # Regular kill (50 XP)
/testkill zone   # Zone kill (150 XP)
```

### 3. Check Console for Errors
Press F8 to open the console and look for any JavaScript errors, especially:
- "Could not find xp-fill element"
- "Could not find xp-text element"
- Any other errors related to XP updates

### 4. Manual Data Sync
Force a data sync from the server:
```
/syncdata
```

### 5. Check XP Bar Updates
After getting a kill or using test commands, you should see in the console:
- `[KOTH] Updated XP bar to: XX%`
- `[KOTH] Updated XP text to: XXX / XXXX XP`
- `[KOTH] XP Progress - Total XP: XXX, Level: X, Progress: XXX, Needed: XXXX, Percentage: XX`

## Common Issues and Fixes

### Issue: XP Bar Not Updating
**Symptoms**: Kill rewards show but XP bar doesn't move

**Check**:
1. Open F8 console and look for: `[KOTH] ERROR: Could not find xp-fill element!`
2. If you see this error, the HTML elements might not be loaded

**Fix**: 
- Restart the resource: `/restart koth_teamsel`
- Make sure you've selected a team first

### Issue: XP Shows Wrong Progress
**Symptoms**: XP bar shows incorrect percentage or wrong numbers

**Check**:
1. Compare server stats (`/checkstats`) with what's shown in the UI
2. Check if your level matches between server and client

**Fix**:
- The level calculations now match exactly between server and client
- Use `/syncdata` to force a refresh

### Issue: XP Resets or Doesn't Save
**Symptoms**: XP goes back to old value after gaining some

**Check**:
1. Check if database is saving properly
2. Look for database errors in server console

**Fix**:
- Make sure MySQL database is running
- Check database connection settings

## Debug Commands Summary
- `/checkstats` - View your current stats from server
- `/testkill` - Test regular kill reward (50 XP + $50)
- `/testkill zone` - Test zone kill reward (150 XP + $150)
- `/syncdata` - Force sync player data from server
- `/simulatekill` - Simulate a kill event (server-side)
- `/testkillpopup` - Test just the kill popup UI

## Expected Behavior
When you get a kill:
1. Kill popup appears showing XP and money gained
2. XP bar animates to new percentage
3. XP text updates to show current/needed XP
4. Money updates in the HUD
5. Data saves to database

## Level Requirements (for reference)
- Level 1: 0 XP
- Level 2: 100 XP
- Level 3: 250 XP
- Level 4: 500 XP
- Level 5: 1000 XP
- Level 10: 8500 XP
- Level 20: 56000 XP
- Level 50: 400000 XP
- Level 50+: Each level needs 10000 more XP
