MAPS = MAPS or {}
MAPS['scrapyard'] = {
    friendlyName = "Scrapyard",
    Spawns = {
        red = { coords = vector3(288.34, -340.94, 44.92), radius = 200.0 },
        green={ coords = vector3(1068.74, -2181.75, 31.54), radius = 200.0 },
        blue ={ coords = vector3(-1598.73, -920.55, 8.98), radius = 200.0 }
    },
    Hill = { coords = vector3(-458.511, -1610.417, 39.142), radius = 200.0 },
    keyPoints = {
        vector3(-423.47, -1685.13, 26.47),
        vector3(-458.39, -1672.18, 28.47),
        vector3(-605.42, -1620.32, 54.0),
        vector3(-574.98, -1697.5, 28.07),
        vector3(-494.97, -1752.09, 27.36),
        vector3(-347.41, -1540.88, 30.44)
    },
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(282.81, -344.28, 44.92), heading = 297.0,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(290.83, -347.31, 44.92), heading = 32.23, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(335.81, -343.30, 47.67), heading = 152.76, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(323.27, -339.25, 47.76), heading = 152.76, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(284.98, -336.14, 44.91), heading = 243.77, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(296.23, -340.98, 44.92), heading = 113.39, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(291.73, -349.34, 44.45), heading = 291.35},
        Spawnpoints = {
            Cars = { coords = vector3(290.94, -380.1, 45.77), heading = 247.94 },
            Helicopters = { coords = vector3(254.92, -366.64, 45.13), heading = 251.48 },
        },
        RespawnVehicle = {coords = vector3(274.932,-331.371,44.917), heading = 161.575},
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(1084.138, -2164.838, 31.40108), heading = 125.99,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(1070.35, -2159.29, 32.39), heading = 143.981, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(1073.95, -2188.36, 31.26), heading = 19.79, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(1062.264, -2186.18, 31.333), heading = 84.083, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(1083.63, -2178.474609375, 31.38), heading = 70.86, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(1065.77, -2170.96, 31.98), heading = 218.27, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(1073.18, -2153.94, 32.36), heading = 45.639 },
        Spawnpoints = {
            Cars = {coords = vector3(1060.29, -2170.43, 32.61), heading = 353.71 },
            Helicopters = {coords = vector3(1047.54, -2280.39, 30.56), heading = 355.048 },
        },
        RespawnVehicle = {coords = vector3(1051.292,-2168.743,31.807), heading = 354.331},
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(-1606.567, -928.0815, 8.986135), heading = 5.0, model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(-1599.58, -910.84, 9.1), heading = 95.05, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(-1624.49, -890.474, 9.092), heading = 138.0, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-1640.705, -876.28, 9.096), heading = 137.835, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-1624.25, -919.60, 8.70), heading = 291.96, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(-1592.61, -925.93, 8.98), heading = 59.53, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(-1597.55, -911.7, 8.96), heading = 3.34 },
        Spawnpoints = {
            Cars = {coords = vector3(-1591.88, -890.04, 10.32), heading = 318.57 },
            Helicopters = {coords = vector3(-1633.27, -903.85, 9.28), heading = 229.73 },
        },
        RespawnVehicle = {coords = vector3(-1611.244,-913.714,8.925), heading = 320.315},
    }
}