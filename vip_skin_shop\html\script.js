let isShopOpen = false;

// Listen for messages from the game
window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.type === 'openShop') {
        openShop(data.vipStatus);
    } else if (data.type === 'closeShop') {
        closeShop();
    }
});

function openShop(vipStatus) {
    if (!vipStatus) {
        return; // Don't open if not VIP
    }
    
    isShopOpen = true;
    document.getElementById('container').classList.remove('hidden');
    
    // Add some entrance animation
    const panel = document.querySelector('.shop-panel');
    panel.style.transform = 'translateX(-100%)';
    panel.style.opacity = '0';
    
    setTimeout(() => {
        panel.style.transition = 'all 0.5s ease-out';
        panel.style.transform = 'translateX(0)';
        panel.style.opacity = '1';
    }, 50);
}

function closeShop() {
    isShopOpen = false;
    
    const panel = document.querySelector('.shop-panel');
    panel.style.transition = 'all 0.3s ease-in';
    panel.style.transform = 'translateX(-100%)';
    panel.style.opacity = '0';
    
    setTimeout(() => {
        document.getElementById('container').classList.add('hidden');
        
        // Send close message to game
        fetch(`https://${GetParentResourceName()}/closeShop`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        });
    }, 300);
}

function buySkin(skinId, weaponHash) {
    // Send purchase request to game
    fetch(`https://${GetParentResourceName()}/buySkin`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
            skinId: skinId,
            weaponHash: weaponHash
        })
    });
    
    // Visual feedback
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'COMPRANDO...';
    button.disabled = true;
    
    setTimeout(() => {
        button.textContent = 'COMPRADO!';
        button.style.background = '#28a745';
        
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
            button.style.background = '';
        }, 2000);
    }, 1000);
}

// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all tabs
            tabButtons.forEach(tab => tab.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Here you could load different weapon categories
            const category = this.dataset.category;
            console.log('Switched to category:', category);
        });
    });
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();
        const skinItems = document.querySelectorAll('.skin-item');
        
        skinItems.forEach(item => {
            const skinName = item.querySelector('h4').textContent.toLowerCase();
            if (skinName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});

// Close shop when clicking outside
document.getElementById('container').addEventListener('click', function(e) {
    if (e.target === this) {
        closeShop();
    }
});

// Close shop with ESC key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && isShopOpen) {
        closeShop();
    }
});

// Utility function to get resource name
function GetParentResourceName() {
    return 'vip_skin_shop';
}
