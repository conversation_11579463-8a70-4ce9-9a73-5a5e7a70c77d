-- Assault class abilities
local activeAmmoBags = {}
local placingAmmoBag = false
local assaultCooldown = 0

-- Debug print function
local function debugPrint(...)
    if Config.Debug then
        print('[KOTH Classes - Assault]', ...)
    end
end

-- Handle assault ability use
RegisterNetEvent('koth_classes:useAssaultAbility')
AddEventHandler('koth_classes:useAssaultAbility', function(ability)
    debugPrint('Using assault ability:', ability.name)
    
    if not placingAmmoBag then
        placingAmmoBag = true
        
        -- Create placement preview
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        
        -- Show instructions
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("Press E to place Ammo Bag, or ESC to cancel")
        EndTextCommandThefeedPostTicker(false, true)
        
        -- Placement thread
        Citizen.CreateThread(function()
            local placed = false
            
            while placingAmmoBag and not placed do
                Citizen.Wait(0)
                
                -- Get placement position (in front of player)
                local playerPed = PlayerPedId()
                local playerCoords = GetEntityCoords(playerPed)
                local playerHeading = GetEntityHeading(playerPed)
                
                -- Calculate position 2 meters in front of player
                local placeX = playerCoords.x + math.cos(math.rad(playerHeading)) * 2.0
                local placeY = playerCoords.y + math.sin(math.rad(playerHeading)) * 2.0
                
                -- Get ground Z coordinate
                local foundGround, groundZ = GetGroundZFor_3dCoord(placeX, placeY, playerCoords.z + 10.0, false)
                local placeZ = foundGround and groundZ or playerCoords.z
                
                -- Draw preview marker
                DrawMarker(
                    Config.AmmoZone.markerType,
                    placeX, placeY, placeZ,
                    0.0, 0.0, 0.0, -- Direction
                    0.0, 0.0, 0.0, -- Rotation
                    ability.resupplyRadius * 2.0, ability.resupplyRadius * 2.0, 1.0, -- Scale
                    Config.AmmoZone.color.r, Config.AmmoZone.color.g, Config.AmmoZone.color.b, Config.AmmoZone.color.a,
                    Config.AmmoZone.bobUpAndDown, true, 2, Config.AmmoZone.rotate, nil, nil, false
                )
                
                -- Check for placement input
                if IsControlJustPressed(0, 38) then -- E key
                    placed = true
                    placingAmmoBag = false
                    
                    -- Play placement animation
                    local playerPed = PlayerPedId()
                    local animDict = "anim@heists@narcotics@trash"
                    local animName = "drop_front"
                    
                    -- Load animation
                    RequestAnimDict(animDict)
                    while not HasAnimDictLoaded(animDict) do
                        Citizen.Wait(1)
                    end
                    
                    -- Freeze player during animation
                    FreezeEntityPosition(playerPed, true)
                    
                    -- Play animation
                    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, 1500, 0, 0, false, false, false)
                    
                    -- Show progress notification
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Placing Ammo Bag...")
                    EndTextCommandThefeedPostTicker(false, true)
                    
                    -- Wait for animation to complete
                    Citizen.Wait(1500)
                    
                    -- Unfreeze player
                    FreezeEntityPosition(playerPed, false)
                    
                    -- Place the ammo bag
                    TriggerServerEvent('koth_classes:placeAmmoBag', {
                        x = placeX,
                        y = placeY,
                        z = placeZ,
                        ability = ability
                    })
                    
                    -- Set cooldown
                    exports['koth_classes']:SetAbilityCooldown(ability.slot, ability.cooldown)
                    assaultCooldown = GetGameTimer() + (ability.cooldown * 1000)
                    
                    -- Clean up animation
                    RemoveAnimDict(animDict)
                    
                    debugPrint('Ammo bag placed at:', placeX, placeY, placeZ)
                    
                elseif IsControlJustPressed(0, 177) then -- ESC/BACKSPACE
                    placingAmmoBag = false
                    
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Ammo Bag placement cancelled")
                    EndTextCommandThefeedPostTicker(false, true)
                end
            end
        end)
    end
end)

-- Receive ammo bag placement from server
RegisterNetEvent('koth_classes:ammoBagPlaced')
AddEventHandler('koth_classes:ammoBagPlaced', function(data)
    debugPrint('Ammo bag placed by player:', data.source, 'at', data.x, data.y, data.z)
    
    -- Create the ammo bag prop
    local model = GetHashKey(data.ability.prop)
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Citizen.Wait(1)
    end
    
    local prop = CreateObject(model, data.x, data.y, data.z, false, false, false)
    SetEntityHeading(prop, 0.0)
    PlaceObjectOnGroundProperly(prop)
    FreezeEntityPosition(prop, true)
    
    -- Store ammo bag data
    local ammoBagId = data.id
    activeAmmoBags[ammoBagId] = {
        prop = prop,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability,
        endTime = GetGameTimer() + (data.ability.duration * 1000)
    }
    
    -- Create ammo resupply zone thread
    Citizen.CreateThread(function()
        local ammoBag = activeAmmoBags[ammoBagId]
        
        while ammoBag and GetGameTimer() < ammoBag.endTime do
            Citizen.Wait(0)
            
            -- Draw ammo zone marker
            DrawMarker(
                Config.AmmoZone.markerType,
                ammoBag.x, ammoBag.y, ammoBag.z,
                0.0, 0.0, 0.0, -- Direction
                0.0, 0.0, 0.0, -- Rotation
                ammoBag.ability.resupplyRadius * 2.0, ammoBag.ability.resupplyRadius * 2.0, 2.0, -- Scale
                Config.AmmoZone.color.r, Config.AmmoZone.color.g, Config.AmmoZone.color.b, Config.AmmoZone.color.a,
                Config.AmmoZone.bobUpAndDown, true, 2, Config.AmmoZone.rotate, nil, nil, false
            )
            
            -- Check if player is in ammo zone
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local distance = #(playerCoords - vector3(ammoBag.x, ammoBag.y, ammoBag.z))
            
            if distance <= ammoBag.ability.resupplyRadius then
                -- Player is in ammo zone
                local currentWeapon = GetSelectedPedWeapon(playerPed)
                
                if currentWeapon and currentWeapon ~= `WEAPON_UNARMED` then
                    local maxAmmo = GetMaxAmmoInClip(playerPed, currentWeapon, true)
                    local currentAmmo = GetAmmoInPedWeapon(playerPed, currentWeapon)
                    
                    -- Check if player needs ammo (less than max)
                    if currentAmmo < (maxAmmo * 10) then -- Give 10 clips worth
                        -- Resupply ammo
                        SetPedAmmo(playerPed, currentWeapon, maxAmmo * 10)
                        
                        -- Show resupply effect (only once every 2 seconds to avoid spam)
                        if GetGameTimer() % 2000 < 100 then
                            BeginTextCommandThefeedPost("STRING")
                            AddTextComponentSubstringPlayerName("Ammo Resupplied!")
                            EndTextCommandThefeedPostTicker(false, false)
                        end
                    end
                end
            end
        end
        
        -- Remove ammo bag when expired
        if activeAmmoBags[ammoBagId] then
            removeAmmoBag(ammoBagId)
        end
    end)
    
    SetModelAsNoLongerNeeded(model)
end)

-- Remove ammo bag
function removeAmmoBag(ammoBagId)
    local ammoBag = activeAmmoBags[ammoBagId]
    if ammoBag then
        if DoesEntityExist(ammoBag.prop) then
            DeleteObject(ammoBag.prop)
        end
        activeAmmoBags[ammoBagId] = nil
        debugPrint('Ammo bag removed:', ammoBagId)
    end
end

-- Receive ammo bag removal from server
RegisterNetEvent('koth_classes:removeAmmoBag')
AddEventHandler('koth_classes:removeAmmoBag', function(ammoBagId)
    removeAmmoBag(ammoBagId)
end)

-- Clean up on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    -- Remove all active ammo bags
    for ammoBagId, _ in pairs(activeAmmoBags) do
        removeAmmoBag(ammoBagId)
    end
end)

debugPrint('Assault abilities loaded')

-- MULTIPLE KEY DETECTION METHODS - ROBUST SOLUTION
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- Check if player is assault class
        local currentClass = exports['koth_classes']:GetCurrentClass()
        if currentClass == 'assault' then
            -- Try multiple key detection methods
            local key5Pressed = false
            
            -- Method 1: Standard control
            if IsControlJustPressed(0, 161) then -- 5 key (INPUT_SELECT_WEAPON_SMG)
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 1 (control 161)')
            end
            
            -- Method 2: Alternative control
            if IsControlJustPressed(1, 161) then -- 5 key with different input group
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 2 (group 1, control 161)')
            end
            
            -- Method 3: Direct key code
            if IsDisabledControlJustPressed(0, 161) then
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 3 (disabled control)')
            end
            
            -- Method 4: Raw key detection
            if IsInputJustPressed(0, 161) then
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 4 (raw input)')
            end
            
            if key5Pressed then
                print('[KOTH Classes] Key 5 DEFINITELY pressed - executing testammobag command')
                
                -- Check cooldown first
                local currentTime = GetGameTimer()
                
                if currentTime < assaultCooldown then
                    local remaining = math.ceil((assaultCooldown - currentTime) / 1000)
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName(string.format("Ammo Bag on cooldown: %d seconds", remaining))
                    EndTextCommandThefeedPostTicker(false, true)
                    print('[KOTH Classes] Ammo bag on cooldown:', remaining, 'seconds')
                else
                    -- Set cooldown immediately
                    assaultCooldown = GetGameTimer() + (60 * 1000) -- 60 seconds
                    
                    -- Execute the testammobag command directly
                    ExecuteCommand('testammobag')
                    
                    print('[KOTH Classes] SUCCESS: Executed testammobag command via key 5')
                    
                    -- Also show notification
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Ammo Bag activated via key 5!")
                    EndTextCommandThefeedPostTicker(false, true)
                end
            end
        end
    end
end)

-- Alternative key binding using RegisterKeyMapping (more reliable)
RegisterKeyMapping('assault_ability', 'Use Assault Ability', 'keyboard', '5')
RegisterCommand('assault_ability', function()
    print('[KOTH Classes] assault_ability command triggered!')
    
    -- Check if player is assault class
    local currentClass = exports['koth_classes']:GetCurrentClass()
    if currentClass == 'assault' then
        print('[KOTH Classes] Player is assault, executing ammo bag ability')
        
        -- Check cooldown first
        local currentTime = GetGameTimer()
        
        if currentTime < assaultCooldown then
            local remaining = math.ceil((assaultCooldown - currentTime) / 1000)
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName(string.format("Ammo Bag on cooldown: %d seconds", remaining))
            EndTextCommandThefeedPostTicker(false, true)
            print('[KOTH Classes] Ammo bag on cooldown:', remaining, 'seconds')
        else
            -- Set cooldown immediately
            assaultCooldown = GetGameTimer() + (60 * 1000) -- 60 seconds
            
            -- Execute the testammobag command directly
            ExecuteCommand('testammobag')
            
            print('[KOTH Classes] SUCCESS: Executed testammobag via RegisterKeyMapping')
            
            -- Show notification
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName("Ammo Bag activated!")
            EndTextCommandThefeedPostTicker(false, true)
        end
    else
        print('[KOTH Classes] Player is not assault class, current class:', currentClass)
    end
end, false)
