# KOTH Classes System

A class-based ability system for the KOTH gamemode that adds special abilities to different player classes.

## Features

- **Class Selection Integration**: Seamlessly integrates with the existing class selection menu
- **Ability System**: Each class can have unique abilities with cooldowns
- **Hotbar Integration**: Abilities are automatically added to the hotbar
- **Database Persistence**: Player class selections are saved to the database
- **Medic Class**: First implemented class with healing abilities

## Current Classes

### Medic (Level 5 Required)
- **Ability**: Med Bag
- **Hotbar Slot**: 5
- **Icon**: `images/ifak.png`
- **Cooldown**: 60 seconds
- **Effect**: Places a healing station that heals all players within 5 meters
- **Healing Rate**: 5 HP per second
- **Duration**: 30 seconds

## How It Works

1. **Class Selection**: When a player selects a class through the existing menu, the `koth:classSelected` event is triggered
2. **Class Assignment**: The class is saved to the database and synced with the player
3. **Ability Setup**: Class abilities are automatically added to the hotbar
4. **Ability Usage**: Players can use abilities by pressing the corresponding hotbar key (1-5)
5. **Cooldown Management**: Abilities have cooldowns that are displayed in the hotbar

## Medic Ability Usage

1. Select the Medic class from the Classes menu (requires level 5)
2. The Med Bag will appear in hotbar slot 5
3. Press `5` to activate the placement mode
4. A green preview circle will show where the med bag will be placed
5. Press `E` to place the med bag or `ESC` to cancel
6. The med bag will heal anyone inside the green circle for 30 seconds
7. After placement, there's a 60-second cooldown before you can place another

## Database Schema

The system adds the following to the database:

```sql
-- Added to koth_players table
ALTER TABLE koth_players ADD COLUMN player_class VARCHAR(50) DEFAULT NULL;

-- New table for cooldowns
CREATE TABLE koth_class_cooldowns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    txid VARCHAR(50) NOT NULL,
    ability_slot INT NOT NULL,
    cooldown_end BIGINT NOT NULL,
    UNIQUE KEY unique_player_ability (txid, ability_slot)
);
```

## Configuration

Edit `shared/config.lua` to modify class abilities:

```lua
Config.Classes = {
    medic = {
        name = "Medic",
        description = "Support class that can deploy healing stations",
        requiredLevel = 5,
        abilities = {
            {
                name = "Med Bag",
                slot = 5,
                icon = "images/ifak.png",
                cooldown = 60, -- seconds
                healRadius = 5.0,
                healAmount = 5, -- HP per second
                duration = 30, -- seconds
                prop = "prop_ld_health_pack"
            }
        }
    }
}
```

## Adding New Classes

To add a new class:

1. Add the class configuration to `Config.Classes` in `shared/config.lua`
2. Create a new ability file in `client/abilities/classname.lua`
3. Add the ability logic following the medic example
4. Update the `fxmanifest.lua` to include the new ability file

## Events

### Client Events
- `koth:classSelected` - Triggered when a player selects a class
- `koth_classes:setPlayerClass` - Sets the player's class on the client
- `koth_classes:useMedicAbility` - Triggers medic ability usage
- `koth_classes:medBagPlaced` - Syncs med bag placement to all clients

### Server Events
- `koth_classes:selectClass` - Saves class selection to database
- `koth_classes:requestPlayerClass` - Requests player's class from database
- `koth_classes:placeMedBag` - Handles med bag placement server-side

## Exports

### Client Exports
- `GetCurrentClass()` - Returns the player's current class
- `SetAbilityCooldown(slot, duration)` - Sets ability cooldown

### Server Exports
- `GetPlayerClass(source)` - Returns a player's class

## Dependencies

- `koth_teamsel` - For class selection integration
- `hotbar` - For ability hotbar integration
- `mysql-async` or `oxmysql` - For database operations

## Installation

1. Ensure the resource is in your resources folder
2. Add `ensure koth_classes` to your server.cfg after `koth_teamsel`
3. The database tables will be created automatically on first run
4. Restart your server

## Troubleshooting

- **Med bag not appearing in hotbar**: Make sure you have the hotbar resource running
- **Can't place med bag**: Check if you're in placement mode (green preview should appear)
- **Healing not working**: Ensure you're standing within the green circle
- **Class not saving**: Check database connection and permissions

## Future Classes (Planned)

- **Assault**: Standard soldier with balanced abilities
- **Engineer**: Can deploy turrets or barriers
- **Heavy**: Tank class with armor abilities
- **Scout**: Fast movement and reconnaissance abilities
