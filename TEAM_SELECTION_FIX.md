# Team Selection UI Fix

## Issue
The team selection UI shows "Loading team data..." and the team counts are not updating (showing 0 for all teams).

## Root Cause
1. The loading text is not being hidden when team counts are received
2. The team counts might not be properly initialized on the server

## Fix Applied

### 1. JavaScript Fix (script.js)
Updated the team selection handler to properly hide the loading text when counts are received:
- Changed from using `.classList.add('hidden')` to `.style.display = 'none'`
- Added console logging to track when loading elements are hidden
- Now hides both `.loading-text` and `.loading-indicator` elements

### 2. Server-Side Fix Needed
The server might need to ensure team counts are properly initialized and sent. The current flow is:
1. Client requests counts via `koth:requestCounts`
2. Server responds with `koth:updateCounts` 
3. Client shows team selection with counts

## Testing Steps
1. Restart the resource
2. Join the server
3. The team selection should show with:
   - No "Loading team data..." text
   - Proper team counts (or 0 if no players)
   - Ability to select a team

## Additional Notes
- The team counts are tracked server-side in the `teamCounts` table
- The counts should update when players join/leave teams
- The HUD uses a different event (`koth:updateTeamCounts`) for ongoing updates
