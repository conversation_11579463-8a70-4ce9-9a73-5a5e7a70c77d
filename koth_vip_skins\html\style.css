/* VIP Weapon Skin Changer Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
}

.skin-changer-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 900px;
    height: 600px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    border: 2px solid #ffd700;
    overflow: hidden;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    color: #1a1a2e;
}

.title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.vip-badge {
    background: #ff6b35;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.title h1 {
    font-size: 24px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.close-btn {
    background: none;
    border: none;
    font-size: 32px;
    color: #1a1a2e;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(26, 26, 46, 0.1);
    transform: rotate(90deg);
}

/* Main Content */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    padding: 30px;
    height: calc(100% - 160px);
}

/* Weapon Info Panel */
.weapon-info-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.weapon-icon {
    text-align: center;
    margin-bottom: 20px;
}

.weapon-icon-placeholder {
    font-size: 64px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.weapon-details h2 {
    color: #ffd700;
    font-size: 20px;
    margin-bottom: 10px;
    text-align: center;
}

.weapon-details p {
    color: #b8b8b8;
    font-size: 14px;
    text-align: center;
    margin-bottom: 15px;
}

.current-skin {
    background: rgba(255, 215, 0, 0.1);
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.current-skin span:first-child {
    color: #b8b8b8;
    display: block;
    margin-bottom: 5px;
}

.current-skin span:last-child {
    color: #ffd700;
    font-weight: bold;
}

/* Skin Selection Panel */
.skin-selection-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.skin-selection-panel h3 {
    color: #ffd700;
    font-size: 18px;
    margin-bottom: 20px;
    text-align: center;
}

.skin-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    max-height: 350px;
    overflow-y: auto;
}

.skin-option {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.skin-option:hover {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.5);
    transform: translateY(-2px);
}

.skin-option.selected {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.skin-color {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 auto 10px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.skin-name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

/* Preview Panel */
.preview-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.preview-panel h3 {
    color: #ffd700;
    font-size: 18px;
    margin-bottom: 20px;
    text-align: center;
}

.preview-container {
    text-align: center;
}

.preview-info {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.preview-info p {
    color: #ffd700;
    margin-bottom: 8px;
    font-size: 14px;
}

.preview-info p:first-child {
    font-size: 16px;
    font-weight: bold;
}

.preview-btn {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preview-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(255, 215, 0, 0.3);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.2);
}

.action-btn {
    padding: 15px 40px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.cancel-btn {
    background: linear-gradient(45deg, #ff4757, #ff6b7a);
    color: white;
}

.cancel-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(255, 71, 87, 0.3);
}

.apply-btn {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
    color: white;
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(46, 213, 115, 0.3);
}

.apply-btn:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 215, 0, 0.3);
    border-top: 4px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: #ffd700;
    font-size: 18px;
    font-weight: bold;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #2ed573, #7bed9f);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    animation: slideInRight 0.5s ease-out;
    z-index: 1000;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scrollbar Styling */
.skin-grid::-webkit-scrollbar {
    width: 8px;
}

.skin-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.skin-grid::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

.skin-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.7);
}
