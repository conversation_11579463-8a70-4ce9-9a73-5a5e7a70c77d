# Admin Panel XP/Level Fix

## Problem
When using the admin panel:
- Setting a player to level 15 worked correctly
- But giving 100 XP would reset them back to level 4
- This happened because the level calculation only went up to level 10

## Solution
Updated both the `CalculateLevel` and `SetPlayerLevel` functions to handle levels beyond 10:

### Level Requirements
```lua
Level 1: 0 XP
Level 2: 100 XP
Level 3: 250 XP
Level 4: 500 XP
Level 5: 1,000 XP
Level 6: 1,750 XP
Level 7: 2,750 XP
Level 8: 4,000 XP
Level 9: 6,000 XP
Level 10: 8,500 XP
Level 11: 11,000 XP
Level 12: 14,000 XP
Level 13: 17,500 XP
Level 14: 21,500 XP
Level 15: 26,000 XP
Level 16: 31,000 XP
Level 17: 36,500 XP
Level 18: 42,500 XP
Level 19: 49,000 XP
Level 20: 56,000 XP
Level 25: 100,000 XP
Level 30: 150,000 XP
Level 40: 250,000 XP
Level 50: 400,000 XP
```

### How It Works Now

1. **Setting Level Directly**: When you set a player to level 15, it automatically sets their XP to 26,000 (the minimum XP for level 15)

2. **Giving XP**: When you give XP, it properly calculates the level based on total XP:
   - If a player is level 15 (26,000 XP) and you give them 100 XP
   - They'll have 26,100 XP total
   - They'll remain level 15 (since level 16 requires 31,000 XP)

3. **Formula for High Levels**:
   - Levels 1-50 use the predefined table
   - After level 50, each level requires 10,000 more XP
   - For levels 11-50 not in the table, each level requires 2,500 more XP than the previous

## Testing
1. Set a player to level 15: `/setlevel [playerID] 15`
2. Give them 100 XP: `/givexp [playerID] 100`
3. They should remain level 15 with 26,100 XP

## Admin Commands
- `/givemoney [playerID] [amount]` - Add money to player
- `/givexp [playerID] [amount]` - Add XP to player (level calculated automatically)
- `/setlevel [playerID] [level]` - Set player to specific level (XP set automatically)
- `/resetstats [playerID]` - Reset player to level 1 with default stats
