local isVIP = false
local shopOpen = false

-- Check VIP status
RegisterNetEvent('vip_skin_shop:setVIPStatus')
AddEventHandler('vip_skin_shop:setVIPStatus', function(status)
    isVIP = status
end)

-- Open shop command
RegisterCommand('skinshop', function()
    if not isVIP then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[VIP SKINS]", "You need VIP access to use the skin shop!"}
        })
        return
    end
    
    if not shopOpen then
        openSkinShop()
    else
        closeSkinShop()
    end
end)

function openSkinShop()
    shopOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = "openShop",
        vipStatus = isVIP
    })
end

function closeSkinShop()
    shopOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = "closeShop"
    })
end

-- NUI Callbacks
RegisterNUICallback('closeShop', function(data, cb)
    closeSkinShop()
    cb('ok')
end)

RegisterNUICallback('buySkin', function(data, cb)
    TriggerServerEvent('vip_skin_shop:buySkin', data.skinId, data.weaponHash)
    cb('ok')
end)

RegisterNUICallback('applySkin', function(data, cb)
    local playerPed = PlayerPedId()
    local weaponHash = GetHashKey(data.weaponHash)
    
    if HasPedGotWeapon(playerPed, weaponHash, false) then
        SetPedWeaponTintIndex(playerPed, weaponHash, data.tintIndex)
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"[VIP SKINS]", "Skin applied successfully!"}
        })
    else
        TriggerEvent('chat:addMessage', {
            color = {255, 255, 0},
            multiline = true,
            args = {"[VIP SKINS]", "You don't have this weapon!"}
        })
    end
    cb('ok')
end)

-- Check VIP status on resource start
Citizen.CreateThread(function()
    Wait(1000)
    TriggerServerEvent('vip_skin_shop:checkVIP')
end)
