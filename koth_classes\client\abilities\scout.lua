-- Scout class abilities
local activeAbilities = {}

-- Scout doesn't have active abilities yet, but we can add passive bonuses
RegisterNetEvent('koth:scoutClassSelected', function()
    local playerPed = PlayerPedId()
    
    -- Scout passive abilities could include:
    -- - Increased movement speed
    -- - Better accuracy
    -- - Reduced weapon sway
    -- - Stealth bonuses
    
    print('[KOTH] Scout class selected - precision marksman abilities active')
end)

-- Cleanup when switching classes
RegisterNetEvent('koth:cleanupScoutAbilities', function()
    -- Clean up any scout-specific effects
    activeAbilities = {}
end)

-- Scout could have abilities like:
-- - Spotting enemies for team
-- - Temporary invisibility/stealth
-- - Deployable motion sensors
-- - Enhanced zoom/steady aim
