# VIP Skin Changer Testing Guide

## Pre-Testing Setup

### 1. Verify Installation
- [ ] Resource folder `koth_vip_skins` is in your resources directory
- [ ] Database table `koth_vip_skins` exists
- [ ] Resource is added to `server.cfg`
- [ ] Main KOTH resource (`koth_teamsel`) is running
- [ ] oxmysq<PERSON> is working

### 2. Test Account Setup
- [ ] Have a VIP test account ready
- [ ] Have a non-VIP test account for negative testing
- [ ] Verify VIP status works in main KOTH resource

## Testing Checklist

### Basic Functionality
- [ ] **Command Access (VIP)**: VIP player can use `/vipskin`
- [ ] **Command Denied (Non-VIP)**: Non-VIP gets "VIP only" message
- [ ] **Weapon Required**: Shows error when no weapon is held
- [ ] **UI Opens**: Skin changer interface appears
- [ ] **UI Closes**: Can close with X button, Cancel, or ESC

### UI Functionality
- [ ] **Weapon Info**: Displays correct weapon name and hash
- [ ] **Skin Grid**: Shows all 8 available tints with colors
- [ ] **Click Selection**: Can select skins by clicking
- [ ] **Drag & Drop**: Can drag skins to preview area
- [ ] **Visual Feedback**: Selection highlights and animations work
- [ ] **Current Skin**: Shows currently applied skin correctly

### Skin Application
- [ ] **Live Preview**: Weapon tint changes when selecting skins
- [ ] **Apply Button**: Successfully applies and saves skin
- [ ] **Persistence**: Skin is remembered after closing/reopening
- [ ] **Auto-Apply**: Saved skin applies when switching to weapon
- [ ] **Multiple Weapons**: Different skins for different weapons

### Visual Effects
- [ ] **Opening Animation**: Smooth slide-in animation
- [ ] **Closing Animation**: Smooth slide-out animation
- [ ] **Selection Effects**: Hover and selection animations
- [ ] **Drop Effects**: Drag and drop visual feedback
- [ ] **Application Effect**: Golden flash when applying skin
- [ ] **Loading Overlay**: Shows when applying skin

### Audio (Optional)
- [ ] **UI Sounds**: Click, drag, drop sounds (if files present)
- [ ] **Application Sound**: Weapon purchase sound on apply
- [ ] **Error Sound**: Error sound for invalid actions

### Database
- [ ] **Save Functionality**: Skins save to database correctly
- [ ] **Load Functionality**: Saved skins load on resource start
- [ ] **Update Functionality**: Changing skins updates database
- [ ] **Multiple Players**: Each player has separate skin data

### Error Handling
- [ ] **No Weapon Error**: Proper error when no weapon held
- [ ] **VIP Check Error**: Graceful handling if VIP check fails
- [ ] **Database Error**: Handles database connection issues
- [ ] **UI Error**: Handles missing UI files gracefully

## Test Scenarios

### Scenario 1: First Time VIP User
1. Join server with VIP account
2. Spawn a weapon (e.g., `/spawnvehicle` then get weapon)
3. Use `/vipskin` command
4. Select and apply a skin
5. Verify skin is applied and saved

### Scenario 2: Returning VIP User
1. Join server with VIP account that has saved skins
2. Spawn a weapon with saved skin
3. Verify skin auto-applies
4. Open skin changer and verify current skin is highlighted

### Scenario 3: Multiple Weapons
1. Apply different skins to 2-3 different weapons
2. Switch between weapons
3. Verify each weapon has its correct skin
4. Verify skins persist after resource restart

### Scenario 4: Non-VIP User
1. Join server with non-VIP account
2. Try to use `/vipskin` command
3. Verify access is denied with proper message

### Scenario 5: Edge Cases
1. Try `/vipskin` with no weapon
2. Try `/vipskin` with melee weapon
3. Try `/vipskin` with special weapons
4. Test with multiple players simultaneously

## Performance Testing

### Client Performance
- [ ] **UI Responsiveness**: Interface responds quickly to interactions
- [ ] **Memory Usage**: No significant memory leaks
- [ ] **Frame Rate**: No FPS drops when using skin changer

### Server Performance
- [ ] **Database Queries**: Efficient database operations
- [ ] **Event Handling**: Proper event cleanup
- [ ] **Resource Usage**: Minimal server resource impact

## Common Issues & Solutions

### "VIP only" message for VIP players
- Check VIP system in main KOTH resource
- Verify Discord roles are configured correctly
- Check badger_discord_roles functionality

### UI doesn't open
- Check F8 console for JavaScript errors
- Verify all HTML/CSS/JS files are present
- Check resource started without errors

### Skins don't save
- Check server console for MySQL errors
- Verify database table exists and is accessible
- Test oxmysql connection

### 3D preview not working
- This is expected - GTA V has limitations with weapon object previews
- The live weapon preview on player still works
- Focus on the weapon tint application functionality

## Success Criteria

✅ **Core Functionality**: All basic features work as expected
✅ **VIP Integration**: Properly integrates with existing VIP system  
✅ **User Experience**: Smooth, intuitive interface
✅ **Data Persistence**: Skins save and load correctly
✅ **Performance**: No significant impact on server/client performance
✅ **Error Handling**: Graceful handling of edge cases

## Final Checklist

- [ ] All test scenarios pass
- [ ] No console errors during normal operation
- [ ] Performance is acceptable
- [ ] Documentation is complete and accurate
- [ ] Ready for production use

**Testing Complete!** 🎉

The VIP Skin Changer is ready for your players to enjoy!
