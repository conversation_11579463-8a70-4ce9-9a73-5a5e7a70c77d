MAPS = MAPS or {}
MAPS['littleseoul'] = {
    friendlyName = "Little Seoul",
    Spawns = {
        red = { coords = vector3(-1645.306,-192.092,55.279), heading = 150.236, radius = 200.0 },
        green = { coords = vector3(-1054.642,-2016.04,13.155), heading = 138.898, radius = 200.0 },
        blue = { coords = vector3(427.53,-981.877,30.695), heading = 0.0, radius = 200.0 }
    },
    Hill = { coords = vector3(-637.872,-957.56,21.495), radius = 225.0 },
    keyPoints = {
        vector3(148.44, -1040.19, 29.89),
        vector3(23.91, -1074.49, 43.89),
        vector3(74.0, -876.53, 31.87),
        vector3(42.21, -1005.48, 35.13),
        vector3(289.33, -999.11, 53.71)
    },
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(-1648.312,-215.486,55.025), heading = 357.165, model = 'cs_priest'},
            { type = 'Vehicles', coords = vector3(-1656.804,-209.591,55.144), heading = 291.969, model = 'u_m_y_zombie_01'},
            { type = 'Ammo', coords = vector3(-1600.497,-222.251,54.935), heading = 62.362, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-1611.785,-250.391,54.06), heading = 76.535, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-1657.688,-194.413,55.532), heading = 249.449, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(-1639.78,-202.299,55.144), heading = 133.228, model = 'a_c_crow' },
        },
        CarModel = { coords = vector3(-1658.677,-209.908,54.993), heading = 201.26 },
        Spawnpoints = {
            Cars = { coords = vector3(-1633.714,-211.846,54.588), heading = 243.78 },
            Helicopters = {coords = vector3(-1645.582,-247.371,54.925), heading = 255.118 },
        },
        RespawnVehicle = {coords = vector3(-1640.083,-230.571,54.875), heading = 342.992},
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(-1057.516,-2031.429,13.155), heading = 357.165,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(-1068.765,-2018.295,13.171), heading = 249.449, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(-1081.332,-2049.653,13.121), heading = 320.315, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-1065.099,-2066.545,13.289), heading = 317.48, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-1046.426,-2023.174,13.155), heading = 133.228, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(-1061.578,-2007.508,13.155), heading = 172.913, model = 'a_c_chop' },
        },
        CarModel = { coords = vector3(-1070.413,-2017.134,13.003), heading = 155.906 },
        Spawnpoints = {
            Cars = {coords = vector3(-1069.306,-2039.512,12.716), heading = 42.52 },
            Helicopters = {coords = vector3(-1045.147,-2070.171,13.694), heading = 42.52 },
        },
        RespawnVehicle = {coords = vector3(-1098.554,-2036.558,13.34), heading = 317.48},
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(409.226,-973.516,29.415), heading = 235.276, model = 's_f_y_cop_01'},
            { type = 'Vehicles', coords = vector3(409.516,-983.248,29.263), heading = 323.15, model = 's_m_y_cop_01'},
            { type = 'Ammo', coords = vector3(445.727,-1033.53,31.47), heading = 187.087, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(427.952,-1035.692,30.055), heading = 189.921, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(418.655,-991.648,29.314), heading = 87.874, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(416.835,-969.521,29.432), heading = 141.732, model = 'a_c_pig' },
        },
        CarModel = { coords = vector3(408.132,-984.422,29.094), heading = 232.441 },
        Spawnpoints = {
            Cars = {coords = vector3(430.813,-957.297,28.825), heading = 87.874 },
            Helicopters = {coords = vector3(418.259,-1020.936,29.499), heading = 87.874 },
        },
        RespawnVehicle = {coords = vector3(395.644,-982.193,29.381), heading = 178.583},
    },
}