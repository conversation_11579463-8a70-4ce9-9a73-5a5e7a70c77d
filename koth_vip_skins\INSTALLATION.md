# VIP Skin Changer Installation Guide

## Quick Setup

### 1. Add to server.cfg
Add this line to your `server.cfg` file:
```
ensure koth_vip_skins
```

### 2. Database Setup
Execute this SQL command in your MySQL database:
```sql
CREATE TABLE IF NOT EXISTS koth_vip_skins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    txid VARCHAR(50) NOT NULL,
    weapon_hash VARCHAR(20) NOT NULL,
    tint_id INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_player_weapon (txid, weapon_hash),
    INDEX idx_txid (txid),
    INDEX idx_weapon_hash (weapon_hash)
);
```

### 3. Restart Server
Restart your FiveM server or use:
```
restart koth_vip_skins
```

## Testing

1. Join your server with a VIP account
2. Equip any weapon
3. Type `/vipskin` in chat
4. The VIP skin changer should open!

## Troubleshooting

### Command says "VIP only"
- Make sure your account has VIP status in the main KOTH resource
- Check that badger_discord_roles is working
- Verify your Discord roles are configured correctly

### UI doesn't open
- Check F8 console for JavaScript errors
- Ensure all files are present in the resource folder
- Verify the resource started without errors

### Skins don't save
- Check server console for MySQL errors
- Verify the database table was created successfully
- Ensure oxmysql is working properly

## Features Overview

✅ **VIP-Only Access** - Only VIP players can use the skin changer
✅ **Live Preview** - See skins applied in real-time
✅ **Drag & Drop** - Intuitive skin selection interface  
✅ **Persistent Storage** - Skins are saved and auto-applied
✅ **Modern UI** - Beautiful, animated interface
✅ **Sound Effects** - Audio feedback for interactions
✅ **Auto-Apply** - Saved skins automatically apply to weapons

Enjoy your new VIP skin changer! 🎨✨
