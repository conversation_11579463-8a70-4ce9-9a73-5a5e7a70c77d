MAPS = MAPS or {}
MAPS['paleto'] = {
    friendlyName = "Paleto",
    Spawns = {
        red = { coords = vector3(-1558.233,4970.756,61.867), heading = 232.441, radius = 250.0 },
        blue ={ coords = vector3(1562.796,6456.896,23.854), heading = 5.669, radius = 250.0 }
    },
    Hill = { coords = vector3(-238.695,6295.609,31.504), radius = 400.0 }, -- Completed
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(-1550.532,4971.02,61.969), heading = 206.929,model = 's_m_y_marine_03'}, -- completed
            { type = 'Vehicles', coords = vector3(-1557.178,4962.409,61.783), heading = 269.291, model = 's_m_m_marine_01'}, -- completed
            { type = 'Ammo', coords = vector3(-1560.712,4922.505,61.547), heading = 51.024, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-1585.846,4896.448,61.295), heading = 51.024, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-1542.132,4974.488,62.204), heading = 230.00, model = 'gr_prop_gr_bench_03a'}, -- completed vector3(-1542.765,4974.989,62.154), heading = 227.0
            { type = 'Cosmic', coords = vector3(-1557.547,4979.367,61.918), heading = 195.591, model = 'a_c_shepherd' }
        },
        CarModel = { coords = vector3(-1559.011,4962.738,61.615), heading = 172.913 }, -- Completed
        Spawnpoints = {
            Cars = { coords = vector3(-1542.804,4957.991,61.969), heading = 317.48 },
            Helicopters = { coords = vector3(-1548.541,4942.127,61.75), heading = 314.646 },
        },
        RespawnVehicle = {coords = vector3(-1558.154,4952.782,61.851), heading = 136.063},
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(1555.3365, 6463.1699, 23.3667), heading = 254.26,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(1569.3615, 6461.6738, 24.4557), heading = 133.86, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(1491.7185, 6446.1890, 22.2433), heading = 346.65, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(1458.3826, 6457.2705, 21.3450), heading = 337.25, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(1559.40, 6451.54, 23.80), heading = 240.94, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(1590.615, 6432.281, 25.26313), heading = 22.75985, model = 'a_c_shepherd' }
        },
        CarModel = { coords = vector3(1570.9155, 6463.6919, 24.5663), heading = 224.34 },
        Spawnpoints = {
            Cars = {coords = vector3(1564.2086, 6428.4492, 25.4719), heading = 65.197 },
            Helicopters = {coords = vector3(1465.5295, 6460.2935, 21.4863), heading = 65.197 },
        },
        RespawnVehicle = {coords = vector3(1581.033,6444.976,25.0), heading = 102.047},
    }
}