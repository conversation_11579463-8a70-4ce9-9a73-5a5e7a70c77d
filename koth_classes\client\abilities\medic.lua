-- Medic class abilities
local activeMedBags = {}
local placingMedBag = false
local medicCooldown = 0

-- Seed the random number generator once using current game time to ensure
-- random heal intervals for the medic bag.  Without seeding, <PERSON><PERSON> may
-- return the same sequence each resource start which appears less
-- natural.
math.randomseed(GetGameTimer())

-- Debug print function
local function debugPrint(...)
    if Config.Debug then
        print('[KOTH Classes - Medic]', ...)
    end
end

-- Handle medic ability use
RegisterNetEvent('koth_classes:useMedicAbility')
AddEventHandler('koth_classes:useMedicAbility', function(ability)
    debugPrint('Using medic ability:', ability.name)
    
    if not placingMedBag then
        placingMedBag = true
        
        -- Create placement preview
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        
        -- Show instructions
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("Press E to place Med Bag, or ESC to cancel")
        EndTextCommandThefeedPostTicker(false, true)
        
        -- Placement thread
        Citizen.CreateThread(function()
            local placed = false
            
            while placingMedBag and not placed do
                Citizen.Wait(0)
                
                -- Get placement position (in front of player)
                local playerPed = PlayerPedId()
                local playerCoords = GetEntityCoords(playerPed)
                local playerHeading = GetEntityHeading(playerPed)
                
                -- Calculate position 2 meters in front of player
                local placeX = playerCoords.x + math.cos(math.rad(playerHeading)) * 2.0
                local placeY = playerCoords.y + math.sin(math.rad(playerHeading)) * 2.0
                
                -- Get ground Z coordinate
                local foundGround, groundZ = GetGroundZFor_3dCoord(placeX, placeY, playerCoords.z + 10.0, false)
                local placeZ = foundGround and groundZ or playerCoords.z
                
                -- Draw preview marker
                DrawMarker(
                    Config.HealingZone.markerType,
                    placeX, placeY, placeZ,
                    0.0, 0.0, 0.0, -- Direction
                    0.0, 0.0, 0.0, -- Rotation
                    ability.healRadius * 2.0, ability.healRadius * 2.0, 1.0, -- Scale
                    Config.HealingZone.color.r, Config.HealingZone.color.g, Config.HealingZone.color.b, Config.HealingZone.color.a,
                    Config.HealingZone.bobUpAndDown, true, 2, Config.HealingZone.rotate, nil, nil, false
                )
                
                -- Check for placement input
                if IsControlJustPressed(0, 38) then -- E key
                    placed = true
                    placingMedBag = false
                    
                    -- Play placement animation
                    local playerPed = PlayerPedId()
                    local animDict = "anim@heists@narcotics@trash"
                    local animName = "drop_front"
                    
                    -- Load animation
                    RequestAnimDict(animDict)
                    while not HasAnimDictLoaded(animDict) do
                        Citizen.Wait(1)
                    end
                    
                    -- Freeze player during animation
                    FreezeEntityPosition(playerPed, true)
                    
                    -- Play animation
                    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, 1500, 0, 0, false, false, false)
                    
                    -- Show progress notification
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Placing Med Bag...")
                    EndTextCommandThefeedPostTicker(false, true)
                    
                    -- Wait for animation to complete
                    Citizen.Wait(1500)
                    
                    -- Unfreeze player
                    FreezeEntityPosition(playerPed, false)
                    
                    -- Place the med bag
                    TriggerServerEvent('koth_classes:placeMedBag', {
                        x = placeX,
                        y = placeY,
                        z = placeZ,
                        ability = ability
                    })
                    
                    -- Set cooldown
                    exports['koth_classes']:SetAbilityCooldown(ability.slot, ability.cooldown)
                    medicCooldown = GetGameTimer() + (ability.cooldown * 1000)
                    
                    -- Clean up animation
                    RemoveAnimDict(animDict)
                    
                    debugPrint('Med bag placed at:', placeX, placeY, placeZ)
                    
                elseif IsControlJustPressed(0, 177) then -- ESC/BACKSPACE
                    placingMedBag = false
                    
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Med Bag placement cancelled")
                    EndTextCommandThefeedPostTicker(false, true)
                end
            end
        end)
    end
end)

-- Receive med bag placement from server
RegisterNetEvent('koth_classes:medBagPlaced')
AddEventHandler('koth_classes:medBagPlaced', function(data)
    debugPrint('Med bag placed by player:', data.source, 'at', data.x, data.y, data.z)
    
    -- Create the med bag prop
    local model = GetHashKey(data.ability.prop)
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Citizen.Wait(1)
    end
    
    local prop = CreateObject(model, data.x, data.y, data.z, false, false, false)
    SetEntityHeading(prop, 0.0)
    PlaceObjectOnGroundProperly(prop)
    FreezeEntityPosition(prop, true)
    
    -- Store med bag data
    local medBagId = data.id
    activeMedBags[medBagId] = {
        prop = prop,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability,
        endTime = GetGameTimer() + (data.ability.duration * 1000),
        -- Track the next time this med bag should heal a player.  When
        -- first placed, allow an immediate heal by setting this to 0.
        nextHealTime = 0
    }
    
    -- Create healing zone thread
    Citizen.CreateThread(function()
        local medBag = activeMedBags[medBagId]
        
        while medBag and GetGameTimer() < medBag.endTime do
            Citizen.Wait(0)
            
            -- Draw healing zone marker
            DrawMarker(
                Config.HealingZone.markerType,
                medBag.x, medBag.y, medBag.z,
                0.0, 0.0, 0.0, -- Direction
                0.0, 0.0, 0.0, -- Rotation
                medBag.ability.healRadius * 2.0, medBag.ability.healRadius * 2.0, 2.0, -- Scale
                Config.HealingZone.color.r, Config.HealingZone.color.g, Config.HealingZone.color.b, Config.HealingZone.color.a,
                Config.HealingZone.bobUpAndDown, true, 2, Config.HealingZone.rotate, nil, nil, false
            )
            
            -- Check if player is in healing zone
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local distance = #(playerCoords - vector3(medBag.x, medBag.y, medBag.z))
            
            if distance <= medBag.ability.healRadius then
                -- Player is in healing zone
                local currentHealth = GetEntityHealth(playerPed)
                local maxHealth = GetEntityMaxHealth(playerPed)
                
                -- Throttle healing so players are not instantly restored to
                -- full health.  Only heal if the player is below max
                -- health and the next heal time has been reached.
                if currentHealth < maxHealth and GetGameTimer() >= medBag.nextHealTime then
                    -- Apply heal (5 HP by default via ability.healAmount)
                    SetEntityHealth(playerPed, math.min(currentHealth + medBag.ability.healAmount, maxHealth))
                    -- Schedule the next heal between 2 and 4 seconds from now
                    local delay = math.random(2000, 4000)
                    medBag.nextHealTime = GetGameTimer() + delay
                    -- Show healing effect once per heal
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("+" .. tostring(medBag.ability.healAmount) .. " HP")
                    EndTextCommandThefeedPostTicker(false, false)
                end
            end
        end
        
        -- Remove med bag when expired
        if activeMedBags[medBagId] then
            removeMedBag(medBagId)
        end
    end)
    
    SetModelAsNoLongerNeeded(model)
end)

-- Remove med bag
function removeMedBag(medBagId)
    local medBag = activeMedBags[medBagId]
    if medBag then
        if DoesEntityExist(medBag.prop) then
            DeleteObject(medBag.prop)
        end
        activeMedBags[medBagId] = nil
        debugPrint('Med bag removed:', medBagId)
    end
end

-- Receive med bag removal from server
RegisterNetEvent('koth_classes:removeMedBag')
AddEventHandler('koth_classes:removeMedBag', function(medBagId)
    removeMedBag(medBagId)
end)

-- Clean up on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    -- Remove all active med bags
    for medBagId, _ in pairs(activeMedBags) do
        removeMedBag(medBagId)
    end
end)

debugPrint('Medic abilities loaded')

-- MULTIPLE KEY DETECTION METHODS - ROBUST SOLUTION
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- Check if player is medic class
        local currentClass = exports['koth_classes']:GetCurrentClass()
        if currentClass == 'medic' then
            -- Try multiple key detection methods
            local key5Pressed = false
            
            -- Method 1: Standard control
            if IsControlJustPressed(0, 161) then -- 5 key (INPUT_SELECT_WEAPON_SMG)
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 1 (control 161)')
            end
            
            -- Method 2: Alternative control
            if IsControlJustPressed(1, 161) then -- 5 key with different input group
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 2 (group 1, control 161)')
            end
            
            -- Method 3: Direct key code
            if IsDisabledControlJustPressed(0, 161) then
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 3 (disabled control)')
            end
            
            -- Method 4: Raw key detection
            if IsInputJustPressed(0, 161) then
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 4 (raw input)')
            end
            
            if key5Pressed then
                print('[KOTH Classes] Key 5 DEFINITELY pressed - executing testmedbag command')
                
                -- Check cooldown first
                local currentTime = GetGameTimer()
                
                if currentTime < medicCooldown then
                    local remaining = math.ceil((medicCooldown - currentTime) / 1000)
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName(string.format("Med Bag on cooldown: %d seconds", remaining))
                    EndTextCommandThefeedPostTicker(false, true)
                    print('[KOTH Classes] Med bag on cooldown:', remaining, 'seconds')
                else
                    -- Set cooldown immediately
                    medicCooldown = GetGameTimer() + (60 * 1000) -- 60 seconds
                    
                    -- Execute the testmedbag command directly
                    ExecuteCommand('testmedbag')
                    
                    print('[KOTH Classes] SUCCESS: Executed testmedbag command via key 5')
                    
                    -- Also show notification
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Med Bag activated via key 5!")
                    EndTextCommandThefeedPostTicker(false, true)
                end
            end
        end
    end
end)

-- Alternative key binding using RegisterKeyMapping (more reliable)
RegisterKeyMapping('medic_ability', 'Use Medic Ability', 'keyboard', '5')
RegisterCommand('medic_ability', function()
    print('[KOTH Classes] medic_ability command triggered!')
    
    -- Check if player is medic class
    local currentClass = exports['koth_classes']:GetCurrentClass()
    if currentClass == 'medic' then
        print('[KOTH Classes] Player is medic, executing med bag ability')
        
        -- Check cooldown first
        local currentTime = GetGameTimer()
        
        if currentTime < medicCooldown then
            local remaining = math.ceil((medicCooldown - currentTime) / 1000)
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName(string.format("Med Bag on cooldown: %d seconds", remaining))
            EndTextCommandThefeedPostTicker(false, true)
            print('[KOTH Classes] Med bag on cooldown:', remaining, 'seconds')
        else
            -- Set cooldown immediately
            medicCooldown = GetGameTimer() + (60 * 1000) -- 60 seconds
            
            -- Execute the testmedbag command directly
            ExecuteCommand('testmedbag')
            
            print('[KOTH Classes] SUCCESS: Executed testmedbag via RegisterKeyMapping')
            
            -- Show notification
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName("Med Bag activated!")
            EndTextCommandThefeedPostTicker(false, true)
        end
    else
        print('[KOTH Classes] Player is not medic class, current class:', currentClass)
    end
end, false)
