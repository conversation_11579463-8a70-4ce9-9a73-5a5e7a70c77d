# Quick Kill System Test Guide

## Step 1: Restart the Resource
```
/restart koth_teamsel
```

## Step 2: Set Teams for Testing
In the server console or in-game:
```
Player 1: /setteam red
Player 2: /setteam blue
```

## Step 3: Verify Teams
```
/checkteams
```
This should show both players assigned to their teams.

## Step 4: Test a Kill
Have Player 1 kill Player 2 and watch for:
1. Server console output showing the kill detection
2. Kill reward popup on Player 1's screen
3. Money and XP increase for Player 1

## Step 5: Test Zone Kill
1. Go to the KOTH zone (Quarry area - coordinates: 2842.4216, 2864.8088)
2. Have one player kill another in the zone
3. Should receive $150 and 150 XP (instead of $50 and 50 XP)

## If Kill Shows "Unknown"
1. Check `/damageinfo` to see if damage tracking is working
2. Use `/testkillflow 2` to simulate a kill on player ID 2
3. Check server console for debug output

## Common Issues and Fixes

### Issue: No player data
```
/forceloaddata [player id]
```

### Issue: No teams assigned
```
/setteam red
/setteam blue
```

### Issue: Kill not detected
- Make sure both players have selected teams
- Check that player data is loaded
- Try different weapons
- Check server console for errors

The enhanced kill detection in client_death.l<PERSON> should catch kills even if FiveM's default functions fail.
