body {
  font-family: "Arial", sans-serif;
  /* Lighten the overlay so the background world is slightly more visible */
  background-color: rgba(0, 0, 0, 0.65);
  color: #f3f3f3;
  margin: 0;
  padding: 0;
}

#attachment-menu {
  position: absolute;
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  width: 420px;
  background-color: #2a2a2a;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6);
  border: 1px solid #444;
}

#attachment-menu h2 {
  margin-top: 0;
  font-size: 22px;
  text-align: center;
  font-weight: 600;
}

#attachment-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 15px;
}

.attachment-item {
  width: 100px;
  margin: 6px;
  background-color: #333;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.attachment-item:hover {
  background-color: #505050;
  transform: translateY(-2px);
}

.attachment-item img {
  width: 72px;
  height: 72px;
  margin-bottom: 6px;
}

.attachment-item .name {
  font-size: 13px;
  margin-bottom: 4px;
  color: #e0e0e0;
}

.attachment-item .price {
  font-size: 12px;
  color: #b0b0b0;
}

#close-btn {
  display: block;
  margin: 20px auto 0 auto;
  padding: 10px 24px;
  background-color: #444;
  border: none;
  border-radius: 8px;
  color: #f0f0f0;
  font-size: 15px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

#close-btn:hover {
  background-color: #666;
  transform: translateY(-1px);
}
