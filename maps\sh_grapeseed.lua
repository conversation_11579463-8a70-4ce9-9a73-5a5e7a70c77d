MAPS = MAPS or {}
MAPS['grapeseed'] = {
    friendlyName = "Grapeseed",
    Spawns = {
        red = { coords = vector3(770.4169, 4261.8369, 56.2856), radius = 200.0 },
        green={ coords = vector3(2786.84, 3468.67, 54.79), radius = 200.0 },
        blue ={ coords = vector3(1565.0159, 6460.6851, 24.0606), radius = 200.0 }
    },
    Hill = { coords = vector3(1921.333, 4850.747, 47.06265), radius = 300.0 },
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(778.3165, 4254.6201, 56.1360), heading = 345.06,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(774.4537, 4275.5913, 56.2031), heading = 208.13, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(671.7103, 4233.9585, 54.7950), heading = 8.35, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(643.9294, 4231.2534, 54.6445), heading = 22.07, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(763.84, 4248.83, 55.88), heading = 22.67, model = 'gr_prop_gr_bench_03a'}
        },
        CarModel = { coords = vector3(773.2491, 4277.8472, 56.0294), heading = 275.26 },
        Spawnpoints = {
            Cars = { coords = vector3(715.4434, 4247.4517, 56.4553), heading = 280.4 },
            Helicopters = { coords = vector3(584.5121, 4240.3276, 53.7785), heading = 267.84 },
        }
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(2799.0647, 3499.9612, 54.8700), heading = 155.46,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(2787.9141, 3497.0674, 55.0062), heading = 207.69, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(2774.2083, 3369.4004, 56.0703), heading = 66.33, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(2791.2185, 3404.0801, 55.8127), heading = 60.92, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(2797.75, 3487.22, 55.04), heading = 62.36, model = 'gr_prop_gr_bench_03a'}
        },
        CarModel = { coords = vector3(2786.0103, 3499.8943, 55.0073), heading = 283.98 },
        Spawnpoints = {
            Cars = { coords = vector3(2781.9446, 3473.4348, 56.3066), heading = 158.29 },
            Helicopters = {coords = vector3(2697.8728, 3441.9612, 55.8245), heading = 244.02 },
        }
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(1555.3365, 6463.1699, 23.3667), heading = 254.26,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(1569.3615, 6461.6738, 24.4557), heading = 133.86, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(1491.7185, 6446.1890, 22.2433), heading = 346.65, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(1458.3826, 6457.2705, 21.3450), heading = 337.25, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(1559.40, 6451.54, 23.80), heading = 240.94, model = 'gr_prop_gr_bench_03a'}
        },
        CarModel = { coords = vector3(1570.9155, 6463.6919, 24.5663), heading = 224.34 },
        Spawnpoints = {
            Cars = {coords = vector3(1564.2086, 6428.4492, 25.4719), heading = 247.06 },
            Helicopters = {coords = vector3(1465.5295, 6460.2935, 21.4863), heading = 253.5 },
        }
    }
}