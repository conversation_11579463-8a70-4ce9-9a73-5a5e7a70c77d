-- VIP players list (you can integrate with your VIP system)
local vipPlayers = {
    -- Add Steam IDs of VIP players here
    -- Example: "steam:110000000000000" = true,
}

-- Check if player is VIP
function isPlayerVIP(source)
    local identifier = GetPlayerIdentifier(source, 0)
    return vipPlayers[identifier] or false
end

-- Check VIP status
RegisterServerEvent('vip_skin_shop:checkVIP')
AddEventHandler('vip_skin_shop:checkVIP', function()
    local source = source
    local isVIP = isPlayerVIP(source)
    TriggerClientEvent('vip_skin_shop:setVIPStatus', source, isVIP)
    
    print(string.format("[VIP Skins] Player %s VIP status: %s", GetPlayerName(source), tostring(isVIP)))
end)

-- Handle skin purchase
RegisterServerEvent('vip_skin_shop:buySkin')
AddEventHandler('vip_skin_shop:buySkin', function(skinId, weaponHash)
    local source = source
    
    if not isPlayerVIP(source) then
        print(string.format("[VIP Skins] Non-VIP player %s tried to buy skin", GetPlayerName(source)))
        return
    end
    
    -- Here you would integrate with your economy system
    -- For now, we'll just allow the purchase
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"[VIP SKINS]", "Skin purchased successfully!"}
    })
    
    print(string.format("[VIP Skins] Player %s purchased skin %s for weapon %s", GetPlayerName(source), skinId, weaponHash))
end)

-- Add a player to VIP (admin command)
RegisterCommand('addvip', function(source, args, rawCommand)
    if source == 0 then -- Console only
        if args[1] then
            local targetId = tonumber(args[1])
            if targetId and GetPlayerName(targetId) then
                local identifier = GetPlayerIdentifier(targetId, 0)
                vipPlayers[identifier] = true
                TriggerClientEvent('vip_skin_shop:setVIPStatus', targetId, true)
                print(string.format("[VIP Skins] Added %s to VIP list", GetPlayerName(targetId)))
            end
        end
    end
end, true)
