window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.action === 'showMenu' && data.type === 'attachments') {
        showAttachmentMenu(data);
    } else if (data.action === 'hideAll') {
        hideAttachmentMenu();
    }
});

function showAttachmentMenu(data) {
    const menu = document.getElementById('attachment-menu');
    const weaponName = document.getElementById('weapon-name');
    const attachmentList = document.getElementById('attachment-list');
    
    // Set weapon name
    weaponName.textContent = data.weaponName || 'Unknown Weapon';
    
    // Clear previous attachments
    attachmentList.innerHTML = '';
    
    // Add attachments
    if (data.items && data.items.length > 0) {
        data.items.forEach(attachment => {
            const attachmentDiv = document.createElement('div');
            attachmentDiv.className = 'attachment-item';
            
            // Create image element with better error handling
            const img = document.createElement('img');
            img.src = attachment.image;
            img.alt = attachment.name;
            img.onerror = function() {
                console.error('Failed to load attachment image:', attachment.image);
                /*
                 * When an attachment image fails to load we fall back to a local
                 * copy of the file in our `images/guns` folder.  We strip off any
                 * folder hierarchy from the original path and prepend
                 * `images/guns/` so that attachments behave the same way as
                 * weapon images.  If the specific file still isn’t found then
                 * the error handler on the <img> tag in the HTML will replace
                 * it with the `unarmed.png` placeholder.
                 */
                const fileName = attachment.image.split('/').pop();
                /*
                 * Convert the original filename to our normalised slug form.
                 * We replace spaces, hyphens and periods with underscores and
                 * collapse multiple underscores.  This matches the naming
                 * convention used in `html/images/guns` so attachments load
                 * correctly even when the original image name contained
                 * characters that break NUI loading.
                 */
                let base = fileName;
                let ext = '';
                const lastDot = fileName.lastIndexOf('.');
                if (lastDot !== -1) {
                    base = fileName.substring(0, lastDot);
                    ext = fileName.substring(lastDot); // includes the dot
                }
                // Replace spaces, periods and hyphens with underscores in the base name
                let slug = base.replace(/[\.\s\-]+/g, '_');
                slug = slug.replace(/_+/g, '_');
                // Normalise 'mk' to uppercase 'MK' in attachment names
                slug = slug.replace(/mk_/gi, 'MK_');
                const safeFilename = slug + ext;
                this.src = `images/guns/${safeFilename}`;
                this.onerror = null; // Prevent infinite loop
            };
            
            attachmentDiv.innerHTML = '';
            attachmentDiv.appendChild(img);
            attachmentDiv.innerHTML += `
                <div class="name">${attachment.name}</div>
                <div class="price">$${attachment.price}</div>
            `;
            
            attachmentDiv.addEventListener('click', function() {
                purchaseAttachment(attachment);
            });
            
            attachmentList.appendChild(attachmentDiv);
        });
    } else {
        attachmentList.innerHTML = '<div style="text-align: center; color: #ccc;">No attachments available</div>';
    }
    
    // Show menu
    menu.classList.remove('hidden');
}

function hideAttachmentMenu() {
    const menu = document.getElementById('attachment-menu');
    menu.classList.add('hidden');
}

function purchaseAttachment(attachment) {
    // Send purchase request to client
    fetch(`https://${GetParentResourceName()}/purchaseAttachment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
            name: attachment.name,
            component: attachment.component,
            price: attachment.price
        })
    }).then(resp => resp.json()).then(resp => {
        console.log('Attachment purchase response:', resp);
    });
    
    // Hide menu after purchase
    hideAttachmentMenu();
}

// Close button functionality
document.getElementById('close-btn').addEventListener('click', function() {
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({})
    });
    hideAttachmentMenu();
});

// Helper function to get parent resource name
function GetParentResourceName() {
    return window.location.hostname;
}

// Add hidden class to CSS if not already present
const style = document.createElement('style');
style.textContent = `
    .hidden {
        display: none !important;
    }
`;
document.head.appendChild(style);
