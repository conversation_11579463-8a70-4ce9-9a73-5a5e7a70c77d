MAPS = MAPS or {}
MAPS['construction'] = {
    friendlyName = "Construction",
    Spawns = {
        red = { coords = vector3(2786.90, 3468.67, 54.79), radius = 200.0 },
        green = { coords = vector3(-1144.30,2666.84,18.09), radius = 200.0 },
        blue = { coords = vector3(696.20, 634.14, 128.91), radius = 200.0 },
    },
    Hill = { coords = vector3(1125.65,2381.39,49.881), radius = 300.0 },
    keyPoints = {
        vector3(1055.96, 2309.88, 52.36),
        vector3(1054.88, 2252.37, 52.36),
        vector3(929.88, 2444.69, 55.67),
        vector3(1024.17, 2443.0, 62.27),
        vector3(1131.86, 2179.25, 56.42)
    },
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(2790.91, 3494.03, 54.99), heading = 182.9,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(2781.44, 3488.15, 55.18), heading = 217.19, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(2786.27, 3458.75, 55.51), heading = 70.6, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(2781.35, 3447.73, 55.67), heading = 81.72, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(2795.97, 3482.58, 55.16), heading = 65.20, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(2775.53, 3479.88, 55.35), heading = 252.28, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(2780.04, 3490.09, 55.18), heading = 305.82 },
        Spawnpoints = {
            Cars = { coords = vector3(2768.8, 3443.93, 56.81), heading = 155.56 },
            Helicopters = { coords = vector3(2728.41, 3425.87, 56.57), heading = 240.82 },
        },
        RespawnVehicle = {coords = vector3(2796.673,3466.774,55.245), heading = 155.906},
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(-1149.35,2659.96,18.08), heading = 279.92,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(-1136.04,2670.80,18.09), heading = 164.48, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(-1120.55,2648.73 , 17.564), heading = 42.953, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-1129.54,2641.21, 17.074), heading = 41.671, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-1158.42, 2664.9, 18.09), heading = 238.11, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(-1136.04, 2662.77, 17.89), heading = 51.02, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(-1135.36,2672.55, 17.83), heading = 252.88 },
        Spawnpoints = {
            Cars = { coords = vector3(-1129.05,2657.58, 18.06), heading = 311.08 },
            Helicopters = { coords = vector3(-1174.05,2613.66,15.72), heading = 310.69 },
        },
        RespawnVehicle = {coords = vector3(-1142.769,2651.275,16.794), heading = 314.646},
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(684.74, 624.7, 128.91), heading = 309.97,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(696.54, 620.61, 128.91), heading = 14.85, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(620.72, 607.111, 128.911), heading = 335.526, model = 'csb_mweather'},
            { type = 'Attachments', coords = vector3(703.47, 631.85, 128.89), heading = 68.03, model = 'gr_prop_gr_bench_03a'},
            { type = 'Repair', coords = vector3(598.028, 615.424, 128.911), heading = 335.526, model = 's_m_y_armymech_01'},
            { type = 'Cosmic', coords = vector3(699.68, 645.28, 129.1), heading = 158.74, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(696.45, 618.58, 128.75), heading = 294.4 },
        Spawnpoints = {
            Cars = { coords = vector3(688.2604, 643.931, 129.6814), heading = 247.59 },
            Helicopters = {coords = vector3(652.6, 622.61, 129.37), heading = 339.33 },
        },
        RespawnVehicle = {coords = vector3(710.611,622.919,128.896), heading = 252.283},
    },
}