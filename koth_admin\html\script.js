console.log('[KOTH Admin] Script loading (VIP edition)...');

let currentPlayers = [];
let selectedPlayer = null;
let kothStatus = null;

// Handle messages from game
window.addEventListener('message', function(event) {
    const data = event.data;
    switch (data.action) {
        case 'openPanel':
            openAdminPanel(data);
            break;
        case 'updateData':
            updatePanelData(data);
            break;
        case 'notification':
            showNotification(data.type, data.message);
            break;
        case 'closePanel':
            closeAdminPanel();
            break;
        case 'newReport':
            addOrUpdateReport(data.report);
            break;
        case 'setReports':
            renderReports(data.reports || []);
            break;
    }
});

// Open admin panel
function openAdminPanel(data) {
    document.getElementById('admin-panel').style.display = 'block';

    if (data.players) {
        currentPlayers = data.players;
        renderPlayerList();
    }

    if (data.kothStatus) {
        kothStatus = data.kothStatus;
        updateKothStatus();
    }
}

// Close admin panel
function closeAdminPanel() {
    document.getElementById('admin-panel').style.display = 'none';
    document.getElementById('player-modal').style.display = 'none';
    // Inform the Lua client that the panel was closed so it can
    // release NUI focus.  We still call the closePanel endpoint
    // here, but the Lua handler is careful not to send another
    // closePanel message back to the UI, avoiding an infinite loop.
    fetch(`https://${GetParentResourceName()}/closePanel`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
}

// Update panel data
function updatePanelData(data) {
    if (data.players) {
        currentPlayers = data.players;
        renderPlayerList();
    }

    if (data.kothStatus) {
        kothStatus = data.kothStatus;
        updateKothStatus();
    }
}

// Render player list
function renderPlayerList() {
    const playerList = document.getElementById('player-list');
    const searchTerm = document.getElementById('player-search').value.toLowerCase();

    playerList.innerHTML = '';

    const filteredPlayers = currentPlayers.filter(player =>
        player.name.toLowerCase().includes(searchTerm) ||
        player.id.toString().includes(searchTerm)
    );

    filteredPlayers.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';
        playerItem.onclick = () => openPlayerModal(player);

        playerItem.innerHTML = `
            <div class="player-header">
                <span class="player-name">${player.name}</span>
                <span class="player-id">ID: ${player.id}</span>
            </div>
            <div class="player-stats-row">
                <div class="stat">
                    <span class="stat-label">Money</span>
                    <span class="stat-value">$${player.money.toLocaleString()}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Level</span>
                    <span class="stat-value">${player.level}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">XP</span>
                    <span class="stat-value">${player.xp.toLocaleString()}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">K/D</span>
                    <span class="stat-value">${player.kills}/${player.deaths}</span>
                </div>
            </div>
        `;

        playerList.appendChild(playerItem);
    });
}

// Update KOTH status display
function updateKothStatus() {
    if (!kothStatus) return;

    const statusElement = document.getElementById('round-status');
    const controllingTeam = document.getElementById('controlling-team');
    const redPoints = document.getElementById('red-points');
    const greenPoints = document.getElementById('green-points');
    const bluePoints = document.getElementById('blue-points');

    if (kothStatus.active) {
        statusElement.textContent = 'Active';
        statusElement.className = 'status-value active';
    } else {
        statusElement.textContent = 'Inactive';
        statusElement.className = 'status-value';
    }

    if (controllingTeam) {
        controllingTeam.textContent = kothStatus.controllingTeam || 'None';
        if (kothStatus.controllingTeam) {
            controllingTeam.style.color = getTeamColor(kothStatus.controllingTeam);
        }
    }

    if (kothStatus.zonePoints) {
        if (redPoints) redPoints.textContent = kothStatus.zonePoints.red || 0;
        if (greenPoints) greenPoints.textContent = kothStatus.zonePoints.green || 0;
        if (bluePoints) bluePoints.textContent = kothStatus.zonePoints.blue || 0;
    }
}

// Get team color
function getTeamColor(team) {
    switch(team) {
        case 'red': return '#ff4444';
        case 'green': return '#44ff44';
        case 'blue': return '#4444ff';
        default: return '#ffffff';
    }
}

// Open player modal
function openPlayerModal(player) {
    selectedPlayer = player;

    document.getElementById('modal-player-name').textContent = player.name;
    document.getElementById('modal-player-id').textContent = player.id;
    document.getElementById('modal-player-money').textContent = player.money.toLocaleString();
    document.getElementById('modal-player-level').textContent = player.level;
    document.getElementById('modal-player-xp').textContent = player.xp.toLocaleString();
    document.getElementById('modal-player-kills').textContent = player.kills;
    document.getElementById('modal-player-deaths').textContent = player.deaths;

    // Populate prestige information and token counts.
    const prestigeRankElem = document.getElementById('modal-player-prestige');
    const prestigeTokensElem = document.getElementById('modal-player-prestigeTokens');
    if (prestigeRankElem) {
        prestigeRankElem.textContent = player.prestige_rank || 0;
    }
    if (prestigeTokensElem) {
        const weaponTokens = player.prestige_weapon_tokens || 0;
        const vehicleTokens = player.prestige_vehicle_tokens || 0;
        prestigeTokensElem.textContent = `${weaponTokens}/${vehicleTokens}`;
    }

    // Populate identifiers list.
    const idListElem = document.getElementById('identifier-list');
    if (idListElem) {
        idListElem.innerHTML = '';
        if (Array.isArray(player.identifiers) && player.identifiers.length > 0) {
            player.identifiers.forEach(function(id) {
                const p = document.createElement('div');
                p.className = 'identifier-item';
                p.textContent = id;
                idListElem.appendChild(p);
            });
        } else {
            const p = document.createElement('div');
            p.className = 'identifier-item';
            p.textContent = 'No identifiers available';
            idListElem.appendChild(p);
        }
    }

    document.getElementById('player-modal').style.display = 'flex';
}

// Close player modal
function closePlayerModal() {
    document.getElementById('player-modal').style.display = 'none';
    selectedPlayer = null;
}

// Show notification
function showNotification(type, message) {
    const container = document.getElementById('notification-container');

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    container.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Reports rendering and actions
function renderReports(list) {
    const container = document.getElementById('reports-list');
    if (!container) return;
    container.innerHTML = '';
    list.forEach(r => {
        const item = buildReportItem(r);
        container.appendChild(item);
    });
}

function addOrUpdateReport(report) {
    const container = document.getElementById('reports-list');
    if (!container) return;
    // Try to update if exists
    const existing = container.querySelector(`[data-report-id="${report.id}"]`);
    const item = buildReportItem(report);
    if (existing) {
        container.replaceChild(item, existing);
    } else {
        container.prepend(item);
    }
}

function buildReportItem(r) {
    const wrap = document.createElement('div');
    wrap.className = 'report-item';
    wrap.dataset.reportId = r.id;

    const header = document.createElement('div');
    header.className = 'report-header';
    header.innerHTML = `<span class="report-title">#${r.id} ${escapeHtml(r.playerName || 'Unknown')}</span>`;

    const meta = document.createElement('div');
    meta.className = 'report-meta';
    const txidText = r.txid ? `TXID: ${escapeHtml(r.txid)}` : 'TXID: N/A';
    const ts = r.timestamp ? new Date(r.timestamp * 1000).toLocaleString() : '';
    meta.textContent = `${txidText} • ${ts}`;

    const desc = document.createElement('div');
    desc.className = 'report-desc';
    desc.textContent = r.description || '';

    const actions = document.createElement('div');
    actions.className = 'report-actions';
    const respondBtn = document.createElement('button');
    respondBtn.className = 'btn btn-primary';
    respondBtn.textContent = 'Respond';
    respondBtn.addEventListener('click', function() {
        const response = prompt('Enter response to player:');
        if (response && response.trim() !== '') {
            fetch(`https://${GetParentResourceName()}/answerReport`, {
                method: 'POST', headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: r.id, response: response.trim() })
            });
        }
    });

    const closeBtn = document.createElement('button');
    closeBtn.className = 'btn btn-warning';
    closeBtn.textContent = 'Close';
    closeBtn.addEventListener('click', function() {
        if (confirm('Close and remove this report?')) {
            fetch(`https://${GetParentResourceName()}/closeReport`, {
                method: 'POST', headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: r.id })
            });
            // Optimistic UI remove
            const node = document.querySelector(`[data-report-id="${r.id}"]`);
            if (node && node.parentNode) node.parentNode.removeChild(node);
        }
    });

    actions.appendChild(respondBtn);
    actions.appendChild(closeBtn);

    wrap.appendChild(header);
    wrap.appendChild(meta);
    wrap.appendChild(desc);
    wrap.appendChild(actions);
    return wrap;
}

function escapeHtml(str) {
    return String(str).replace(/[&<>"]/g, s => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;'}[s]));
}

// Refresh button
const refreshReportsBtn = document.getElementById('refresh-reports');
if (refreshReportsBtn) {
    refreshReportsBtn.addEventListener('click', function() {
        fetch(`https://${GetParentResourceName()}/getReports`, {
            method: 'POST', headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    });
}


    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}



// Event Listeners

// Close buttons
document.getElementById('close-panel').addEventListener('click', closeAdminPanel);
document.getElementById('close-modal').addEventListener('click', closePlayerModal);

// Search
document.getElementById('player-search').addEventListener('input', renderPlayerList);

// Refresh players
document.getElementById('refresh-players').addEventListener('click', function() {
    fetch(`https://${GetParentResourceName()}/refreshPlayers`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
});

// KOTH Controls
const startBtn = document.getElementById('start-round');
if (startBtn) {
    startBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to start a new KOTH round?')) {
            fetch(`https://${GetParentResourceName()}/startRound`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
        }
    });
}
const stopBtn = document.getElementById('stop-round');
if (stopBtn) {
    stopBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to stop the current KOTH round?')) {
            fetch(`https://${GetParentResourceName()}/stopRound`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
        }
    });
}

// Delete vehicles button.  Always present in our updated UI; confirm
const deleteBtn = document.getElementById('delete-vehicles');
if (deleteBtn) {
    deleteBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to delete all vehicles on the server?')) {
            fetch(`https://${GetParentResourceName()}/deleteAllVehicles`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
        }
    });
}

// Force next map button
const nextBtn = document.getElementById('next-map');
if (nextBtn) {
    nextBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to force the next map? This will end the current round.')) {
            fetch(`https://${GetParentResourceName()}/forceNextMap`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
        }
    });
}

// Time of day controls
const dayBtn2 = document.getElementById('set-day');
if (dayBtn2) {
    dayBtn2.addEventListener('click', function() {
        fetch(`https://${GetParentResourceName()}/changeTimeOfDay`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ time: 'day' })
        });
    });
}
const nightBtn2 = document.getElementById('set-night');
if (nightBtn2) {
    nightBtn2.addEventListener('click', function() {
        fetch(`https://${GetParentResourceName()}/changeTimeOfDay`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ time: 'night' })
        });
    });
}

// Player Actions
document.getElementById('give-money').addEventListener('click', function() {
    const amount = parseInt(document.getElementById('money-amount').value);
    if (!selectedPlayer || !amount || amount <= 0) {
        showNotification('error', 'Please enter a valid amount');
        return;
    }
    fetch(`https://${GetParentResourceName()}/giveMoney`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            playerId: selectedPlayer.id,
            amount: amount
        })
    });
    document.getElementById('money-amount').value = '';
});

document.getElementById('give-xp').addEventListener('click', function() {
    const amount = parseInt(document.getElementById('xp-amount').value);
    if (!selectedPlayer || !amount || amount <= 0) {
        showNotification('error', 'Please enter a valid amount');
        return;
    }
    fetch(`https://${GetParentResourceName()}/giveXP`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            playerId: selectedPlayer.id,
            amount: amount
        })
    });
    document.getElementById('xp-amount').value = '';
});

document.getElementById('set-level').addEventListener('click', function() {
    const level = parseInt(document.getElementById('level-amount').value);
    if (!selectedPlayer || !level || level < 1 || level > 50) {
        showNotification('error', 'Please enter a valid level (1-50)');
        return;
    }
    fetch(`https://${GetParentResourceName()}/setLevel`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            playerId: selectedPlayer.id,
            level: level
        })
    });
    document.getElementById('level-amount').value = '';
});

document.getElementById('reset-player').addEventListener('click', function() {
    if (!selectedPlayer) return;
    if (confirm(`Are you sure you want to reset all stats for ${selectedPlayer.name}? This cannot be undone!`)) {
        fetch(`https://${GetParentResourceName()}/resetPlayer`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ playerId: selectedPlayer.id })
        });
        closePlayerModal();
    }
});

// Set Class XP
const setClassXpBtn = document.getElementById('set-class-xp');
if (setClassXpBtn) {
    setClassXpBtn.addEventListener('click', function() {
        const classIdInput = document.getElementById('class-id');
        const xpInput = document.getElementById('class-xp');
        if (!selectedPlayer) {
            showNotification('error', 'No player selected');
            return;
        }
        const classId = classIdInput.value.trim();
        const xpVal = parseInt(xpInput.value);
        if (!classId) {
            showNotification('error', 'Please enter a class ID (e.g. assault)');
            return;
        }
        if (isNaN(xpVal) || xpVal < 0) {
            showNotification('error', 'Please enter a valid XP amount');
            return;
        }
        fetch(`https://${GetParentResourceName()}/setClassXP`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                playerId: selectedPlayer.id,
                classId: classId,
                xpAmount: xpVal
            })
        });
        classIdInput.value = '';
        xpInput.value = '';
        showNotification('info', `Setting ${classId} XP for ${selectedPlayer.name} to ${xpVal}`);
    });
}

// Reset prestige
const resetPrestigeBtn = document.getElementById('reset-prestige');
if (resetPrestigeBtn) {
    resetPrestigeBtn.addEventListener('click', function() {
        if (!selectedPlayer) {
            showNotification('error', 'No player selected');
            return;
        }
        if (!confirm(`Are you sure you want to reset prestige for ${selectedPlayer.name}?`)) {
            return;
        }
        fetch(`https://${GetParentResourceName()}/resetPrestige`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ playerId: selectedPlayer.id })
        });
        showNotification('info', `Resetting prestige for ${selectedPlayer.name}`);
        closePlayerModal();
    });
}

// Change team buttons
document.querySelectorAll('.btn-team-select').forEach(function(btn) {
    btn.addEventListener('click', function() {
        const team = this.dataset.team;
        if (!selectedPlayer || !team) {
            showNotification('error', 'No player selected');
            return;
        }
        fetch(`https://${GetParentResourceName()}/forceChangeTeam`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                playerId: selectedPlayer.id,
                team: team
            })
        });
        closePlayerModal();
    });
});

// Give VIP button.  Grabs the Discord ID from the input and posts
// it to the giveVip endpoint.  We do not require a selected player;
// the Discord ID can refer to any user, online or offline.
const giveVipBtn = document.getElementById('give-vip');
if (giveVipBtn) {
    giveVipBtn.addEventListener('click', function() {
        const idInput = document.getElementById('vip-discord');
        const discordId = idInput.value.trim();
        if (!discordId) {
            showNotification('error', 'Please enter a Discord ID');
            return;
        }
        fetch(`https://${GetParentResourceName()}/giveVip`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ discordId: discordId })
        });
        idInput.value = '';
    });
}

// ESC key handler
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        if (document.getElementById('player-modal').style.display === 'flex') {
            closePlayerModal();
        } else {
            closeAdminPanel();
        }
    }
});