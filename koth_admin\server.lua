print('[KOTH Admin] Server loading...')

-- Admin check function (you can customize this based on your server's admin system)
local function isAdmin(source)
    -- For now, using a simple identifier check. You can replace this with your admin system
    local identifiers = GetPlayerIdentifiers(source)
    
  -- List of admin identifiers (add your admin identifiers here)
  local adminIdentifiers = {
    "steam:110000105c7f1da", -- Your Steam ID (if you have one)
    "license:c88fdbd3f2660c309932ec8fcd5e3be1eb2a9c4d", -- Your primary license
    "license2:b2d29abf0f03f0411639f54a9c7ceddc5b181319", -- Your secondary license
    "discord:332035384705810432", -- Your Discord ID
    "fivem:4353894", -- Your FiveM ID
    "live:1055518423139464", -- Your Live ID
    "xbl:2535423827211959", -- Your Xbox Live ID
    "ip:*************", -- Your IP (not recommended for production)
    
    -- Additional admin identifiers provided by the server owner.  These
    -- values correspond to the identifiers printed when a player
    -- attempts to use /kothadmin.  Adding them here will allow
    -- that player to access the admin panel.  You can add or
    -- remove entries as needed.
    
    -- Example identifiers for user _Kvsk:
    "license:5ea036ecc69bf535d514d12c17480800c98f515f",
    "license2:5ea036ecc69bf535d514d12c17480800c98f515f",
    "xbl:2535433305392137",
    "live:1055518864220541",
    "discord:484991363419471885",
    "fivem:7047860"
    , -- Additional admin license for EmuLegs32 (typo difference)
    "license:c88fdbd3f2660c3099932ec8fcd5e3be1eb2a9c4d"
  }
    
    for _, id in ipairs(identifiers) do
        for _, adminId in ipairs(adminIdentifiers) do
            if id == adminId then
                return true
            end
        end
    end
    
    -- Admin check is now properly configured
    return false
end

-- Get player identifiers helper
local function GetPlayerTXID(source)
    local identifiers = GetPlayerIdentifiers(source)
    for _, id in ipairs(identifiers) do
        if string.find(id, "license:") then
            return id
        end
    end
    return nil
end

--[[
  Build a table of current online players keyed by transaction ID.  Each
  record includes server ID, player name, transaction ID and an array
  of identifiers.  These identifiers include license, license2,
  discord, fivem, live, xbl and ip.  They are later sent to the
  admin panel UI so an admin can view a player's credentials when
  selecting them from the list.
]]
local function buildOnlinePlayers()
    local result = {}
    for _, playerId in ipairs(GetPlayers()) do
        local txid = GetPlayerTXID(playerId)
        if txid then
            local ids = GetPlayerIdentifiers(playerId) or {}
            result[txid] = {
                id = playerId,
                name = GetPlayerName(playerId),
                txid = txid,
                identifiers = ids
            }
        end
    end
    return result
end

-- Debug command to check identifiers
RegisterCommand('checkidentifiers', function(source, args, rawCommand)
    if source == 0 then
        print('[KOTH Admin] This command can only be used by players')
        return
    end
    
    local identifiers = GetPlayerIdentifiers(source)
    print('[KOTH Admin] Player identifiers for ' .. GetPlayerName(source) .. ':')
    for _, id in ipairs(identifiers) do
        print('  - ' .. id)
    end
end, false)

-- Command to open admin panel
RegisterCommand('kothadmin', function(source, args, rawCommand)
    if source == 0 then
        print('[KOTH Admin] This command can only be used by players')
        return
    end
    
    -- Debug: Print player identifiers
    local identifiers = GetPlayerIdentifiers(source)
    print('[KOTH Admin] Checking admin access for ' .. GetPlayerName(source))
    print('[KOTH Admin] Player identifiers:')
    for _, id in ipairs(identifiers) do
        print('  - ' .. id)
    end
    
    if not isAdmin(source) then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[KOTH Admin]", "You don't have permission to use this command!"}
        })
        return
    end
    
    -- Build a map of online players keyed by txid.  This includes
    -- identifiers for each online player so the admin panel can
    -- display their credentials.
    local onlinePlayers = buildOnlinePlayers()
    -- Table to send to UI
    local players = {}
    
    -- Query database for all player stats
    exports.oxmysql:execute('SELECT * FROM koth_players', {}, function(results)
        if results then
            for _, row in ipairs(results) do
                -- Check if this player is online
                local onlineData = onlinePlayers[row.txid]
                if onlineData then
                    -- Player is online, use their current server ID
                    table.insert(players, {
                        id = onlineData.id,
                        name = onlineData.name,
                        txid = row.txid,
                        money = row.money or 0,
                        xp = row.xp or 0,
                        level = row.level or 1,
                        kills = row.kills or 0,
                        deaths = row.deaths or 0,
                        prestige_rank = row.prestige_rank or 0,
                        prestige_weapon_tokens = row.prestige_weapon_tokens or 0,
                        prestige_vehicle_tokens = row.prestige_vehicle_tokens or 0,
                        identifiers = onlineData.identifiers or {}
                    })
                    -- Remove from online players list
                    onlinePlayers[row.txid] = nil
                end
            end
        end
        
        -- Add any online players not in database yet
        for txid, playerInfo in pairs(onlinePlayers) do
            table.insert(players, {
                id = playerInfo.id,
                name = playerInfo.name,
                txid = txid,
                money = 0,
                xp = 0,
                level = 1,
                kills = 0,
                deaths = 0,
                prestige_rank = 0,
                prestige_weapon_tokens = 0,
                prestige_vehicle_tokens = 0,
                identifiers = playerInfo.identifiers or {}
            })
        end
        
        -- Get KOTH zone status
        local kothStatus = nil
        local success2, result2 = pcall(function()
            return exports['koth_teamsel']:GetKothZoneStatus()
        end)
        if success2 then
            kothStatus = result2
        end
        
        -- Send data to client
        TriggerClientEvent('koth_admin:openPanel', source, {
            players = players,
            kothStatus = kothStatus
        })
    end)
end, false)

-- Give money to player
RegisterNetEvent('koth_admin:giveMoney', function(targetId, amount)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized giveMoney attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    local moneyAmount = tonumber(amount)
    
    if not targetPlayer or not moneyAmount then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID or amount')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:GivePlayerMoney(targetPlayer, moneyAmount)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Gave $%d to %s', moneyAmount, GetPlayerName(targetPlayer)))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s gave $%d to %s (ID: %d)', 
            GetPlayerName(source), moneyAmount, GetPlayerName(targetPlayer), targetPlayer))
            
        -- Log to money transactions
        local txid = GetPlayerTXID(targetPlayer)
        if txid then
            exports.oxmysql:execute('INSERT INTO koth_money_log (txid, transaction_type, amount, balance_before, balance_after, description) VALUES (?, ?, ?, ?, ?, ?)', {
                txid,
                'admin_give',
                moneyAmount,
                0, -- We don't have the before balance easily accessible now
                0, -- We don't have the after balance easily accessible now
                string.format('Admin %s gave money', GetPlayerName(source))
            })
        end
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to give money - player data not loaded')
    end
end)

-- Give XP to player
RegisterNetEvent('koth_admin:giveXP', function(targetId, amount)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized giveXP attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    local xpAmount = tonumber(amount)
    
    if not targetPlayer or not xpAmount then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID or amount')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:GivePlayerXP(targetPlayer, xpAmount)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Gave %d XP to %s', xpAmount, GetPlayerName(targetPlayer)))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s gave %d XP to %s (ID: %d)', 
            GetPlayerName(source), xpAmount, GetPlayerName(targetPlayer), targetPlayer))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to give XP - player data not loaded')
    end
end)

-- Set player level
RegisterNetEvent('koth_admin:setLevel', function(targetId, level)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized setLevel attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    local newLevel = tonumber(level)
    
    if not targetPlayer or not newLevel then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID or level')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:SetPlayerLevel(targetPlayer, newLevel)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Set %s to level %d', GetPlayerName(targetPlayer), newLevel))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s set %s (ID: %d) to level %d', 
            GetPlayerName(source), GetPlayerName(targetPlayer), targetPlayer, newLevel))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to set level - player data not loaded')
    end
end)

-- Start KOTH round
RegisterNetEvent('koth_admin:startRound', function()
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized startRound attempt from', source)
        return
    end
    
    -- Call the main KOTH resource to start a round
    local success = exports['koth_teamsel']:StartKothRound()
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 'KOTH round started!')
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 215, 0},
            multiline = true,
            args = {"[KOTH]", "A new KOTH round has been started by an admin!"}
        })
        
        -- Log the action
        print(string.format('[KOTH Admin] %s started a KOTH round', GetPlayerName(source)))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to start round')
    end
end)

-- Stop KOTH round
RegisterNetEvent('koth_admin:stopRound', function()
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized stopRound attempt from', source)
        return
    end
    
    -- Call the main KOTH resource to stop the round
    local success = exports['koth_teamsel']:StopKothRound()
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 'KOTH round stopped!')
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[KOTH]", "The KOTH round has been stopped by an admin!"}
        })
        
        -- Log the action
        print(string.format('[KOTH Admin] %s stopped the KOTH round', GetPlayerName(source)))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to stop round')
    end
end)

-- Reset player stats
RegisterNetEvent('koth_admin:resetPlayer', function(targetId)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized resetPlayer attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    
    if not targetPlayer then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:ResetPlayerStats(targetPlayer)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Reset stats for %s', GetPlayerName(targetPlayer)))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s reset stats for %s (ID: %d)', 
            GetPlayerName(source), GetPlayerName(targetPlayer), targetPlayer))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to reset player - player data not loaded')
    end
end)

--[[
  Reset a player's prestige rank and tokens.  When an admin chooses
  this option in the panel, their client will emit koth_admin:resetPrestige
  with the target player's server ID.  The server validates admin
  permissions and then calls into the main KOTH resource to reset
  the player's prestige rank.  The ResetPlayerPrestige export must
  be provided by the koth_teamsel resource; if it returns true the
  admin is notified of success.
]]
RegisterNetEvent('koth_admin:resetPrestige', function(targetId)
    local src = source
    if not isAdmin(src) then
        print('[KOTH Admin] Unauthorized resetPrestige attempt from', src)
        return
    end
    local targetPlayer = tonumber(targetId)
    if not targetPlayer then
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Invalid player ID')
        return
    end
    -- Check online status
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Player is not online')
        return
    end
    -- Call into main resource to reset prestige; assumes the export returns true/false
    local ok = false
    if exports['koth_teamsel'] and exports['koth_teamsel'].ResetPlayerPrestige then
        ok = exports['koth_teamsel']:ResetPlayerPrestige(targetPlayer)
    end
    if ok then
        TriggerClientEvent('koth_admin:notification', src, 'success', ('Reset prestige for %s'):format(GetPlayerName(targetPlayer)))
        print(('[KOTH Admin] %s reset prestige for %s (ID: %d)'):format(GetPlayerName(src), GetPlayerName(targetPlayer), targetPlayer))
        -- Optionally notify the admin to refresh the player list manually.  The UI
        -- can be refreshed using the refresh button to reflect new prestige
        -- values.
    else
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Failed to reset prestige')
    end
end)

--[[
  Set a player's XP for a given class.  The admin supplies the
  target player's server ID, a class identifier (string), and the XP
  amount.  This calls into the koth_classes resource which manages
  per-class XP and will recalculate the class level accordingly.
]]
RegisterNetEvent('koth_admin:setClassXP', function(targetId, classId, xpAmount)
    local src = source
    if not isAdmin(src) then
        print('[KOTH Admin] Unauthorized setClassXP attempt from', src)
        return
    end
    local targetPlayer = tonumber(targetId)
    local xpVal = tonumber(xpAmount)
    if not targetPlayer or not classId or xpVal == nil then
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Invalid parameters for setClassXP')
        return
    end
    -- Check online status
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Player is not online')
        return
    end
    -- Forward to koth_classes resource
    TriggerEvent('koth_classes:setClassXP', targetPlayer, classId, xpVal)
    TriggerClientEvent('koth_admin:notification', src, 'success', ('Set %s XP for %s to %d'):format(classId, GetPlayerName(targetPlayer), xpVal))
    print(('[KOTH Admin] %s set %s class XP for %s (ID: %d) to %d'):format(GetPlayerName(src), classId, GetPlayerName(targetPlayer), targetPlayer, xpVal))
    -- No automatic refresh; admin can refresh player list manually.
end)

-- Refresh player list
RegisterNetEvent('koth_admin:refreshPlayers', function()
    local source = source

    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized refresh attempt from', source)
        return
    end

    -- Build online players map with identifiers
    local onlinePlayers = buildOnlinePlayers()
    local players = {}

    -- Query database for all player stats
    exports.oxmysql:execute('SELECT * FROM koth_players', {}, function(results)
        if results then
            for _, row in ipairs(results) do
                -- Check if this player is online
                local onlineData = onlinePlayers[row.txid]
                if onlineData then
                    table.insert(players, {
                        id = onlineData.id,
                        name = onlineData.name,
                        txid = row.txid,
                        money = row.money or 0,
                        xp = row.xp or 0,
                        level = row.level or 1,
                        kills = row.kills or 0,
                        deaths = row.deaths or 0,
                        prestige_rank = row.prestige_rank or 0,
                        prestige_weapon_tokens = row.prestige_weapon_tokens or 0,
                        prestige_vehicle_tokens = row.prestige_vehicle_tokens or 0,
                        identifiers = onlineData.identifiers or {}
                    })
                    onlinePlayers[row.txid] = nil
                end
            end
        end

        -- Add any online players not in database yet
        for txid, playerInfo in pairs(onlinePlayers) do
            table.insert(players, {
                id = playerInfo.id,
                name = playerInfo.name,
                txid = txid,
                money = 0,
                xp = 0,
                level = 1,
                kills = 0,
                deaths = 0,
                prestige_rank = 0,
                prestige_weapon_tokens = 0,
                prestige_vehicle_tokens = 0,
                identifiers = playerInfo.identifiers or {}
            })
        end

        -- Get KOTH zone status
        local kothStatus = nil
        local success2, result2 = pcall(function()
            return exports['koth_teamsel']:GetKothZoneStatus()
        end)
        if success2 then
            kothStatus = result2
        end

        -- Send updated data
        TriggerClientEvent('koth_admin:updateData', source, {
            players = players,
            kothStatus = kothStatus
        })
    end)
end)

-- Delete all vehicles
RegisterNetEvent('koth_admin:deleteAllVehicles', function()
    local source = source

    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized deleteAllVehicles attempt from', source)
        return
    end

    -- Get all vehicles and delete them
    local vehicleCount = 0
    local allVehicles = GetAllVehicles()

    for i = 1, #allVehicles do
        local vehicle = allVehicles[i]
        if DoesEntityExist(vehicle) then
            -- Check if vehicle has any players in it by checking common seats
            local hasPlayers = false

            -- Check driver seat and passenger seats (most vehicles have 0-7 seats)
            for seat = -1, 7 do
                local ped = GetPedInVehicleSeat(vehicle, seat)
                if ped ~= 0 and IsPedAPlayer(ped) then
                    hasPlayers = true
                    break
                end
            end

            -- Only delete if no players are in the vehicle
            if not hasPlayers then
                DeleteEntity(vehicle)
                vehicleCount = vehicleCount + 1
            end
        end
    end

    -- Send success notification
    TriggerClientEvent('koth_admin:notification', source, 'success',
        string.format('Deleted %d vehicles successfully.', vehicleCount))

    TriggerClientEvent('chat:addMessage', -1, {
        color = {255, 165, 0},
        multiline = true,
        args = {"[KOTH Admin]", string.format("All vehicles have been deleted by an admin! (%d vehicles removed)", vehicleCount)}
    })

    -- Log the action
    print(string.format('[KOTH Admin] %s deleted %d vehicles on the server', GetPlayerName(source), vehicleCount))
end)

-- Force the current round to end and immediately start the next map
-- vote.  This wraps the KOTH resource's ForceNextMap export.  The
-- optional winTeam argument can be provided to credit a specific team
-- with the victory; if omitted, the current controlling team is used
-- or a default team selected by the resource.  Only admins may
-- trigger this action.
RegisterNetEvent('koth_admin:forceNextMap', function(winTeam)
    local src = source
    if not isAdmin(src) then
        print('[KOTH Admin] Unauthorized forceNextMap attempt from', src)
        return
    end
    -- Attempt to call the export; wrap in pcall to avoid crashing if
    -- the export is missing.
    local success, err = pcall(function()
        exports['koth_teamsel']:ForceNextMap(winTeam)
    end)
    if success then
        TriggerClientEvent('koth_admin:notification', src, 'success', 'Forcing next map...')
        print(string.format('[KOTH Admin] %s triggered ForceNextMap', GetPlayerName(src)))
    else
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Failed to force next map')
        print('[KOTH Admin] ForceNextMap error:', err)
    end
end)

-- Force a player to join a specific team.  This calls the KOTH
-- resource's ForcePlayerTeam export, bypassing team limits.  Only
-- admins may trigger this.
RegisterNetEvent('koth_admin:forceChangeTeam', function(targetId, team)
    local src = source
    if not isAdmin(src) then
        print('[KOTH Admin] Unauthorized forceChangeTeam attempt from', src)
        return
    end
    local tid = tonumber(targetId)
    if not tid or not team then
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Invalid player ID or team')
        return
    end
    local success, err = pcall(function()
        exports['koth_teamsel']:ForcePlayerTeam(tid, team)
    end)
    if success then
        TriggerClientEvent('koth_admin:notification', src, 'success', ('Player moved to %s team'):format(team))
        print(string.format('[KOTH Admin] %s forced player %d to team %s', GetPlayerName(src), tid, team))
    else
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Failed to change team')
        print('[KOTH Admin] forceChangeTeam error:', err)
    end
end)

-- Change the in-game time of day.  Admins can broadcast a time
-- override to all players.  Accepts "day" or "night".  Clients
-- handle the actual clock adjustment in the NUI script.
RegisterNetEvent('koth_admin:changeTimeOfDay', function(time)
    local src = source
    if not isAdmin(src) then
        print('[KOTH Admin] Unauthorized changeTimeOfDay attempt from', src)
        return
    end
    if time == 'day' or time == 'night' then
        TriggerClientEvent('koth_admin:setTimeOfDay', -1, time)
        TriggerClientEvent('koth_admin:notification', src, 'success', ('Time set to %s'):format(time))
        print(string.format('[KOTH Admin] %s set time of day to %s', GetPlayerName(src), time))
    else
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Invalid time parameter')
    end
end)

--[[
  Grant VIP status to a player via Discord ID.  This handler
  receives a plain Discord ID (with or without the `discord:` prefix),
  validates admin permissions and triggers the main KOTH resource's
  VIP grant event.  If the ID lacks the `discord:` prefix, it is
  automatically added.  The admin is notified of success or failure.
]]
RegisterNetEvent('koth_admin:giveVip', function(discordId)
    local src = source
    if not isAdmin(src) then
        print('[KOTH Admin] Unauthorized giveVip attempt from', src)
        return
    end
    if not discordId or discordId == '' then
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Invalid Discord ID')
        return
    end
    -- Normalise the Discord ID; prepend prefix if missing
    local id = tostring(discordId)
    if not string.find(id, '^discord:') then
        id = 'discord:' .. id
    end
    -- Wrap in pcall in case the main resource does not implement this event
    local ok, err = pcall(function()
        TriggerEvent('koth:grantVip', id)
    end)
    if ok then
        TriggerClientEvent('koth_admin:notification', src, 'success', ('Granted VIP to %s'):format(id))
        print(string.format('[KOTH Admin] %s granted VIP to %s', GetPlayerName(src), id))
    else
        TriggerClientEvent('koth_admin:notification', src, 'error', 'Failed to grant VIP')
        print('[KOTH Admin] giveVip error:', err)
    end
end)

print('[KOTH Admin] Server loaded successfully')
