/* Stylesheet for the complete KOTH admin panel with VIP support.
 * This file is copied verbatim from the original v6 admin panel.
 */

@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700&display=swap');

body {
  margin: 0;
  padding: 0;
  font-family: 'Rubik', sans-serif;
  color: #fff;
  background: transparent;
  user-select: none;
}

#admin-panel {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  overflow-y: auto;
  z-index: 1000;
  box-sizing: border-box;
  padding: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.panel-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}

.panel-content {
  display: flex;
  gap: 20px;
}

.section {
  background: rgba(0, 0, 0, 0.6);
  padding: 20px;
  border-radius: 8px;
  flex: 1;
  box-sizing: border-box;
}

.koth-control {
  max-width: 350px;
}

.player-management {
  flex: 2;
  display: flex;
  flex-direction: column;
}

.section h2 {
  margin-top: 0;
  font-size: 20px;
  margin-bottom: 10px;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-buttons .btn {
  width: 100%;
}

.time-button-group {
  display: flex;
  gap: 10px;
}

.round-status {
  margin-top: 10px;
}

.status-label {
  font-weight: 500;
}

.status-value.active {
  color: #4caf50;
  font-weight: 600;
}

.zone-info {
  margin-top: 15px;
}

.info-item {
  margin-bottom: 10px;
}

.info-label {
  font-weight: 500;
}

.zone-points {
  display: flex;
  gap: 10px;
}

.team-points {
  font-weight: 500;
}

.team-points.red {
  color: #ff4444;
}
.team-points.green {
  color: #44ff44;
}
.team-points.blue {
  color: #4444ff;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  text-transform: uppercase;
}

.btn-small {
  font-size: 12px;
  padding: 6px 10px;
}

.btn-refresh {
  background-color: #2196f3;
}

.btn-primary {
  background-color: #2196f3;
  color: #fff;
}

.btn-danger {
  background-color: #f44336;
  color: #fff;
}

.btn-warning {
  background-color: #ff9800;
  color: #fff;
}

.btn-primary:hover,
.btn-danger:hover,

/* Reports section styling */
.section.reports { margin-top: 20px; }
.reports-list { display: flex; flex-direction: column; gap: 10px; }
.report-item { background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.08); padding: 10px; border-radius: 6px; }
.report-header { font-weight: 600; margin-bottom: 4px; }
.report-meta { font-size: 12px; color: #bbb; margin-bottom: 8px; }
.report-desc { margin-bottom: 10px; white-space: pre-wrap; }
.report-actions { display: flex; gap: 8px; }

.btn-warning:hover,
.btn-refresh:hover {
  opacity: 0.8;
}

.player-list {
  flex: 1;
  overflow-y: auto;
  margin-top: 10px;
}

.player-item {
  background: rgba(255, 255, 255, 0.05);
  padding: 10px;
  margin-bottom: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.player-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-weight: 600;
}

.player-stats-row {
  display: flex;
  gap: 10px;
  font-size: 12px;
}

.stat {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-weight: 500;
}

.stat-value {
  font-weight: 600;
}

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.modal-content {
  background: #1e1e1e;
  border-radius: 8px;
  width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
}

.player-stats {
  background: rgba(255, 255, 255, 0.05);
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.stat-label {
  font-weight: 500;
}

.action-section {
  margin-bottom: 15px;
}

.action-section h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.input-group {
  display: flex;
  gap: 10px;
}

.input-group input {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
}

.input-group input::placeholder {
  color: #bbb;
}

.team-button-group {
  display: flex;
  gap: 10px;
}

.btn-team-select {
  flex: 1;
}

.danger-zone {
  border-top: 1px solid rgba(255, 0, 0, 0.3);
  padding-top: 10px;
}

/* Notification styles */
#notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1050;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  background: rgba(0, 0, 0, 0.8);
  padding: 10px 15px;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
  opacity: 0.95;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  border-left: 4px solid #4caf50;
}

.notification.error {
  border-left: 4px solid #f44336;
}

.notification.info {
  border-left: 4px solid #2196f3;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 0.95; }
}

@keyframes slideOut {
  from { transform: translateX(0); opacity: 0.95; }
  to { transform: translateX(100%); opacity: 0; }
}