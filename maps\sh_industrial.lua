MAPS = MAPS or {}
MAPS['industrial'] = {
    friendlyName = "Industrial",
    Spawns = {
        red = { coords = vector3(1153.99, -843.35, 54.5), radius = 200.0 },
        green= { coords = vector3(1107.23,  -3132.13, 5.9), radius = 200.0 },
        blue = { coords = vector3(-338.88, -1483.27, 30.59), radius = 200.0 }
    },
    Hill = { coords = vector3(931.73, -2082.42, 30.57), radius = 250.0 },
    keyPoints = {
        vector3(811.24, -2156.22, 29.84),
        vector3(970.42, -2126.19, 30.71),
        vector3(877.46, -1969.57, 69.06)
    },
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(1146.44, -848.18, 54.26), heading = 291.22,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(1148.57, -838.93, 54.56), heading = 243.58, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(1183.97, -842.65, 54.47), heading = 87.07, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(1172.462, -884.442, 53.196), heading = 75.034, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(1153.23, -857.81, 54.04), heading = 345.82, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(1157.08, -834.4, 54.81), heading = 167.24, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(1146.91, -838.34, 54.06), heading = 314.49 },
        Spawnpoints = {
            Cars = { coords = vector3(1183.27, -826.48, 55.91), heading = 154.16 },
            Helicopters = { coords = vector3(1193.23, -789.24, 57.21), heading = 163.31 },
        },
        RespawnVehicle = {coords = vector3(1161.679,-844.431,54.622), heading = 167.244},
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(1115.16, -3127.35, 5.9), heading = 122.9,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(1115.27, -3137.37, 5.9), heading = 60.51, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(1152.624, -3089.314, 5.77) , heading = 178.596, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(1165.691, -3095.745, 5.802), heading = 177.787, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(1108.40, -3124.56, 5.89), heading = 201.259, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(1106.15, -3139.24, 5.89), heading = 337.32, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(1117.22, -3137.97, 5.41), heading = 144.17 },
        Spawnpoints = {
            Cars = {coords = vector3(1107.47, -3115.04, 6.46), heading = 90.45 },
            Helicopters = {coords = vector3(1166.586, -3172.83, 5.801), heading = 357.943 },
        },
        RespawnVehicle = {coords = vector3(1079.393,-3131.367,5.892), heading = 0.0},
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(-327.35, -1482.27, 30.55), heading = 87.62,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(-333.31, -1471.89, 30.55), heading = 179.03, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(-281.17, -1532.99, 27.34), heading = 59.617, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-269.61, -1498.64, 29.82), heading = 68.72, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-335.35, -1490.27, 30.55), heading = 328.81, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(-324.53, -1491.71, 30.61), heading = 53.86, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(-334.29, -1469.69, 30.05), heading = 258.01 },
        Spawnpoints = {
            Cars = {coords = vector3(-327.59, -1456.84, 31.2), heading = 356.44 },
            Helicopters = {coords = vector3(-344.2, -1430.5, 30.28), heading = 270.57 },
        },
        RespawnVehicle = {coords = vector3(-323.631,-1473.389,30.661), heading = 212.598},
    }
}