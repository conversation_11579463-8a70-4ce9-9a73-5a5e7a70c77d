-- VIP Weapon Skin Changer - Server Side
-- Handles VIP permission checking and skin data persistence

local RESOURCE_NAME = GetCurrentResourceName()

-- Check if player has VIP access using the main KOTH resource
local function IsPlayerVIP(source)
    -- Method 1: Try to call the IsPlayerVIP function directly from main resource
    local success, result = pcall(function()
        -- Get the main resource's environment
        local mainResourceName = 'koth_teamsel'
        local mainResource = GetResourceState(mainResourceName)

        if mainResource == 'started' then
            -- Try to access the IsPlayerVIP function from the main resource
            -- This requires the main resource to export this function
            return exports[mainResourceName]:IsPlayerVIP(source)
        end
        return false
    end)

    if success and result ~= nil then
        return result
    end

    -- Method 2: Use the same VIP checking logic as the main resource
    -- This is a fallback that replicates the VIP checking from server.lua
    local function checkVIPRoles(src)
        -- VIP roles from the main resource
        local VIP_ROLES = {
            '1402275621543084092',        -- Role ID
            'Vantage Supporter [VIP Access]' -- Role name
        }

        -- Try to check Discord roles using badger_discord_roles
        local function checkRoles(roles)
            if type(roles) ~= 'table' then return false end
            for _, r in pairs(roles) do
                for _, vip in ipairs(VIP_ROLES) do
                    if tostring(r) == tostring(vip) or
                       (type(r) == 'table' and (tostring(r.id) == tostring(vip) or tostring(r.name) == tostring(vip))) then
                        return true
                    end
                end
            end
            return false
        end

        -- Try to get all roles at once
        local ok, roles = pcall(function()
            return exports['badger_discord_roles']:GetRoles(src)
        end)

        if ok and roles then
            if checkRoles(roles) then
                return true
            end
        end

        -- Fallback: check each configured role via IsRolePresent
        for _, vip in ipairs(VIP_ROLES) do
            local ok2, result2 = pcall(function()
                return exports['badger_discord_roles']:IsRolePresent(src, vip)
            end)
            if ok2 and result2 then
                return true
            end
        end

        return false
    end

    -- Try the fallback method
    local fallbackSuccess, fallbackResult = pcall(checkVIPRoles, source)
    if fallbackSuccess then
        return fallbackResult
    end

    print(('[%s] Warning: Could not check VIP status for player %d, defaulting to false'):format(RESOURCE_NAME, source))
    return false
end

-- Register the /vipskin command
RegisterCommand('vipskin', function(source, args, rawCommand)
    if source == 0 then
        print(('[%s] This command can only be used by players'):format(RESOURCE_NAME))
        return
    end
    
    local playerName = GetPlayerName(source)
    print(('[%s] VIP skin command used by %s (ID: %d)'):format(RESOURCE_NAME, playerName, source))
    
    -- Check if player has VIP access
    if not IsPlayerVIP(source) then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 100, 100},
            args = {'[VIP SKINS]', 'This command is only available to VIP players!'}
        })
        return
    end
    
    -- Player is VIP, open the skin changer
    TriggerClientEvent('koth_vip_skins:open_skin_changer', source)
    
    print(('[%s] Opening VIP skin changer for %s'):format(RESOURCE_NAME, playerName))
end, false)

-- Handle skin application requests
RegisterNetEvent('koth_vip_skins:apply_skin')
AddEventHandler('koth_vip_skins:apply_skin', function(weaponHash, skinData)
    local source = source
    
    -- Verify VIP status again for security
    if not IsPlayerVIP(source) then
        print(('[%s] Unauthorized skin application attempt from %d'):format(RESOURCE_NAME, source))
        return
    end
    
    -- Validate the data
    if not weaponHash or not skinData then
        print(('[%s] Invalid skin data from player %d'):format(RESOURCE_NAME, source))
        return
    end
    
    local playerName = GetPlayerName(source)
    print(('[%s] Applying skin for %s - Weapon: %s, Tint: %d'):format(
        RESOURCE_NAME, playerName, weaponHash, skinData.tint or 0))
    
    -- Apply the skin on the client
    TriggerClientEvent('koth_vip_skins:apply_weapon_skin', source, weaponHash, skinData)
    
    -- Save to database (we'll implement this later)
    SavePlayerSkinPreference(source, weaponHash, skinData)
end)

-- Save player skin preferences to database
function SavePlayerSkinPreference(source, weaponHash, skinData)
    local identifiers = GetPlayerIdentifiers(source)
    local txid = nil
    
    -- Find the license identifier
    for _, id in ipairs(identifiers) do
        if string.find(id, 'license:') then
            txid = id
            break
        end
    end
    
    if not txid then
        print(('[%s] Could not find license for player %d'):format(RESOURCE_NAME, source))
        return
    end
    
    -- Save to database
    MySQL.Async.execute(
        'INSERT INTO koth_vip_skins (txid, weapon_hash, tint_id, created_at) VALUES (?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE tint_id = VALUES(tint_id), updated_at = NOW()',
        {txid, weaponHash, skinData.tint or 0},
        function(affectedRows)
            if affectedRows > 0 then
                print(('[%s] Saved skin preference for %s'):format(RESOURCE_NAME, GetPlayerName(source)))
            end
        end
    )
end

-- Load player skin preferences when they join
RegisterNetEvent('koth_vip_skins:load_preferences')
AddEventHandler('koth_vip_skins:load_preferences', function()
    local source = source
    
    if not IsPlayerVIP(source) then
        return
    end
    
    local identifiers = GetPlayerIdentifiers(source)
    local txid = nil
    
    for _, id in ipairs(identifiers) do
        if string.find(id, 'license:') then
            txid = id
            break
        end
    end
    
    if not txid then
        return
    end
    
    MySQL.Async.fetchAll(
        'SELECT weapon_hash, tint_id FROM koth_vip_skins WHERE txid = ?',
        {txid},
        function(results)
            if results and #results > 0 then
                TriggerClientEvent('koth_vip_skins:load_saved_skins', source, results)
                print(('[%s] Loaded %d saved skins for %s'):format(RESOURCE_NAME, #results, GetPlayerName(source)))
            end
        end
    )
end)

-- Event to check VIP status (fallback method)
RegisterNetEvent('koth_vip_skins:check_vip_status')
AddEventHandler('koth_vip_skins:check_vip_status', function(playerId)
    -- This would be handled by the main KOTH resource
    -- For now, we'll just return false as a fallback
    TriggerEvent('koth_vip_skins:vip_check_response', false, playerId)
end)

print(('[%s] Server loaded successfully'):format(RESOURCE_NAME))
