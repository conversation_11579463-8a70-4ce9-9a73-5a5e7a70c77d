-- VIP Weapon Skin Changer - Server Side
-- Handles VIP permission checking and skin data persistence

local RESOURCE_NAME = GetCurrentResourceName()

-- TEMPORARY: Enable debug mode and VIP bypass for testing
local DEBUG_MODE = true
local TEMP_VIP_BYPASS = true -- Set to false in production

-- Check if player has VIP access using the main KOTH resource
local function IsPlayerVIP(source)
    local playerName = GetPlayerName(source)

    if DEBUG_MODE then
        print(('[%s] Checking VIP status for %s (ID: %d)'):format(RESOURCE_NAME, playerName, source))
    end

    -- TEMPORARY: Bypass VIP check for testing
    if TEMP_VIP_BYPASS then
        if DEBUG_MODE then
            print(('[%s] TEMP BYPASS: Allowing %s access for testing'):format(RESOURCE_NAME, playerName))
        end
        return true
    end

    -- Method 1: Try to call the exported IsPlayerVIP function from main resource
    local success, result = pcall(function()
        local mainResourceName = 'koth_teamsel'
        local mainResource = GetResourceState(mainResourceName)

        if DEBUG_MODE then
            print(('[%s] Main resource state: %s'):format(RESOURCE_NAME, mainResource))
        end

        if mainResource == 'started' then
            return exports[mainResourceName]:IsPlayerVIP(source)
        end
        return false
    end)

    if success and result ~= nil then
        if DEBUG_MODE then
            print(('[%s] Export method result for %s: %s'):format(RESOURCE_NAME, playerName, tostring(result)))
        end
        return result
    else
        if DEBUG_MODE then
            print(('[%s] Export method failed for %s, trying fallback'):format(RESOURCE_NAME, playerName))
        end
    end

    -- Method 2: Direct VIP checking using the same logic as main resource
    local function checkVIPDirect(src)
        -- Get player identifiers
        local identifiers = GetPlayerIdentifiers(src)
        local discordId = nil

        for _, id in ipairs(identifiers) do
            if string.find(id, 'discord:') then
                discordId = id
                break
            end
        end

        print(('[%s] Discord ID for %s: %s'):format(RESOURCE_NAME, playerName, tostring(discordId)))

        if not discordId then
            print(('[%s] No Discord ID found for %s'):format(RESOURCE_NAME, playerName))
            return false
        end

        -- VIP roles from the main resource (matching exactly)
        local VIP_ROLES = {
            '1402275621543084092',        -- Role ID
            'Vantage Supporter [VIP Access]' -- Role name
        }

        -- Check VIP overrides first (from main resource logic)
        local function checkVIPOverrides()
            -- Try to access the main resource's VIP_OVERRIDES
            local ok, hasOverride = pcall(function()
                -- This would need to be implemented in the main resource
                return false -- For now, return false
            end)
            return ok and hasOverride
        end

        if checkVIPOverrides() then
            print(('[%s] VIP override found for %s'):format(RESOURCE_NAME, playerName))
            return true
        end

        -- Check Discord roles using badger_discord_roles
        local function checkRoles(roles)
            if type(roles) ~= 'table' then return false end
            for _, r in pairs(roles) do
                for _, vip in ipairs(VIP_ROLES) do
                    if tostring(r) == tostring(vip) or
                       (type(r) == 'table' and (tostring(r.id) == tostring(vip) or tostring(r.name) == tostring(vip))) then
                        print(('[%s] VIP role match found for %s: %s'):format(RESOURCE_NAME, playerName, tostring(vip)))
                        return true
                    end
                end
            end
            return false
        end

        -- Try to get all roles at once
        local ok, roles = pcall(function()
            return exports['badger_discord_roles']:GetRoles(src)
        end)

        if ok and roles then
            print(('[%s] Got roles for %s: %d roles'):format(RESOURCE_NAME, playerName, #roles))
            if checkRoles(roles) then
                return true
            end
        else
            print(('[%s] Failed to get roles for %s'):format(RESOURCE_NAME, playerName))
        end

        -- Fallback: check each configured role via IsRolePresent
        for _, vip in ipairs(VIP_ROLES) do
            local ok2, result2 = pcall(function()
                return exports['badger_discord_roles']:IsRolePresent(src, vip)
            end)
            if ok2 and result2 then
                print(('[%s] VIP role present for %s: %s'):format(RESOURCE_NAME, playerName, vip))
                return true
            end
        end

        print(('[%s] No VIP roles found for %s'):format(RESOURCE_NAME, playerName))
        return false
    end

    -- Try the direct method
    local directSuccess, directResult = pcall(checkVIPDirect, source)
    if directSuccess then
        print(('[%s] Direct method result for %s: %s'):format(RESOURCE_NAME, playerName, tostring(directResult)))
        return directResult
    end

    print(('[%s] All VIP check methods failed for %s, defaulting to false'):format(RESOURCE_NAME, playerName))
    return false
end

-- Register the /vipskin command
RegisterCommand('vipskin', function(source, args, rawCommand)
    if source == 0 then
        print(('[%s] This command can only be used by players'):format(RESOURCE_NAME))
        return
    end

    local playerName = GetPlayerName(source)
    print(('[%s] VIP skin command used by %s (ID: %d)'):format(RESOURCE_NAME, playerName, source))

    -- Check if player has VIP access
    local isVip = IsPlayerVIP(source)
    print(('[%s] VIP check result for %s: %s'):format(RESOURCE_NAME, playerName, tostring(isVip)))

    if not isVip then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 100, 100},
            args = {'[VIP SKINS]', 'This command is only available to VIP players!'}
        })

        -- Additional debug info
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            args = {'[DEBUG]', 'Use /testvip to debug your VIP status'}
        })
        return
    end

    -- Player is VIP, open the skin changer
    TriggerClientEvent('koth_vip_skins:open_skin_changer', source)

    print(('[%s] Opening VIP skin changer for %s'):format(RESOURCE_NAME, playerName))
end, false)

-- Admin command to toggle VIP bypass and debug mode
RegisterCommand('vipskinconfig', function(source, args, rawCommand)
    if source ~= 0 and not IsPlayerAceAllowed(source, 'koth.admin') then
        if source ~= 0 then
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                args = {'[VIP SKINS]', 'No permission.'}
            })
        end
        return
    end

    local action = args[1] and string.lower(args[1]) or 'status'

    if action == 'bypass' then
        local value = args[2] and string.lower(args[2])
        if value == 'on' or value == 'true' or value == '1' then
            TEMP_VIP_BYPASS = true
            print('[VIP Skins] VIP bypass enabled')
            if source ~= 0 then
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {'[VIP SKINS]', 'VIP bypass enabled'}
                })
            end
        elseif value == 'off' or value == 'false' or value == '0' then
            TEMP_VIP_BYPASS = false
            print('[VIP Skins] VIP bypass disabled')
            if source ~= 0 then
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 255, 0},
                    args = {'[VIP SKINS]', 'VIP bypass disabled'}
                })
            end
        end
    elseif action == 'debug' then
        local value = args[2] and string.lower(args[2])
        if value == 'on' or value == 'true' or value == '1' then
            DEBUG_MODE = true
            print('[VIP Skins] Debug mode enabled')
            if source ~= 0 then
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {'[VIP SKINS]', 'Debug mode enabled'}
                })
            end
        elseif value == 'off' or value == 'false' or value == '0' then
            DEBUG_MODE = false
            print('[VIP Skins] Debug mode disabled')
            if source ~= 0 then
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 255, 0},
                    args = {'[VIP SKINS]', 'Debug mode disabled'}
                })
            end
        end
    else
        -- Show current status
        local status = string.format('VIP Bypass: %s | Debug Mode: %s',
            TEMP_VIP_BYPASS and 'ON' or 'OFF',
            DEBUG_MODE and 'ON' or 'OFF')
        print('[VIP Skins] ' .. status)
        if source ~= 0 then
            TriggerClientEvent('chat:addMessage', source, {
                color = {0, 255, 255},
                args = {'[VIP SKINS]', status}
            })
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 255, 255},
                args = {'[USAGE]', '/vipskinconfig bypass on/off | /vipskinconfig debug on/off'}
            })
        end
    end
end, false)

-- Temporary admin override command for testing (remove in production)
RegisterCommand('vipskinforcevip', function(source, args, rawCommand)
    if source == 0 then
        print('[VIP Skins] This command can only be used by players')
        return
    end

    -- Check if player has admin permissions
    if not IsPlayerAceAllowed(source, 'koth.admin') then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            args = {'[VIP SKINS]', 'No permission.'}
        })
        return
    end

    local playerName = GetPlayerName(source)
    print(('[%s] Admin override VIP skin command used by %s (ID: %d)'):format(RESOURCE_NAME, playerName, source))

    -- Force open the skin changer (bypass VIP check)
    TriggerClientEvent('koth_vip_skins:open_skin_changer', source)

    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        args = {'[VIP SKINS]', 'Admin override - opening skin changer'}
    })

    print(('[%s] Admin override - opening VIP skin changer for %s'):format(RESOURCE_NAME, playerName))
end, false)

-- Handle skin application requests
RegisterNetEvent('koth_vip_skins:apply_skin')
AddEventHandler('koth_vip_skins:apply_skin', function(weaponHash, skinData)
    local source = source
    
    -- Verify VIP status again for security
    if not IsPlayerVIP(source) then
        print(('[%s] Unauthorized skin application attempt from %d'):format(RESOURCE_NAME, source))
        return
    end
    
    -- Validate the data
    if not weaponHash or not skinData then
        print(('[%s] Invalid skin data from player %d'):format(RESOURCE_NAME, source))
        return
    end
    
    local playerName = GetPlayerName(source)
    print(('[%s] Applying skin for %s - Weapon: %s, Tint: %d'):format(
        RESOURCE_NAME, playerName, weaponHash, skinData.tint or 0))
    
    -- Apply the skin on the client
    TriggerClientEvent('koth_vip_skins:apply_weapon_skin', source, weaponHash, skinData)
    
    -- Save to database (we'll implement this later)
    SavePlayerSkinPreference(source, weaponHash, skinData)
end)

-- Save player skin preferences to database
function SavePlayerSkinPreference(source, weaponHash, skinData)
    local identifiers = GetPlayerIdentifiers(source)
    local txid = nil
    
    -- Find the license identifier
    for _, id in ipairs(identifiers) do
        if string.find(id, 'license:') then
            txid = id
            break
        end
    end
    
    if not txid then
        print(('[%s] Could not find license for player %d'):format(RESOURCE_NAME, source))
        return
    end
    
    -- Save to database
    MySQL.Async.execute(
        'INSERT INTO koth_vip_skins (txid, weapon_hash, tint_id, created_at) VALUES (?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE tint_id = VALUES(tint_id), updated_at = NOW()',
        {txid, weaponHash, skinData.tint or 0},
        function(affectedRows)
            if affectedRows > 0 then
                print(('[%s] Saved skin preference for %s'):format(RESOURCE_NAME, GetPlayerName(source)))
            end
        end
    )
end

-- Load player skin preferences when they join
RegisterNetEvent('koth_vip_skins:load_preferences')
AddEventHandler('koth_vip_skins:load_preferences', function()
    local source = source
    
    if not IsPlayerVIP(source) then
        return
    end
    
    local identifiers = GetPlayerIdentifiers(source)
    local txid = nil
    
    for _, id in ipairs(identifiers) do
        if string.find(id, 'license:') then
            txid = id
            break
        end
    end
    
    if not txid then
        return
    end
    
    MySQL.Async.fetchAll(
        'SELECT weapon_hash, tint_id FROM koth_vip_skins WHERE txid = ?',
        {txid},
        function(results)
            if results and #results > 0 then
                TriggerClientEvent('koth_vip_skins:load_saved_skins', source, results)
                print(('[%s] Loaded %d saved skins for %s'):format(RESOURCE_NAME, #results, GetPlayerName(source)))
            end
        end
    )
end)

-- Event to check VIP status (fallback method)
RegisterNetEvent('koth_vip_skins:check_vip_status')
AddEventHandler('koth_vip_skins:check_vip_status', function(playerId)
    -- This would be handled by the main KOTH resource
    -- For now, we'll just return false as a fallback
    TriggerEvent('koth_vip_skins:vip_check_response', false, playerId)
end)

-- Debug command to test VIP status (remove in production)
RegisterCommand('testvip', function(source, args, rawCommand)
    if source == 0 then
        print('[VIP Skins] This command can only be used by players')
        return
    end

    local playerName = GetPlayerName(source)
    local isVip = IsPlayerVIP(source)

    print(('[VIP Skins] VIP Test for %s (ID: %d): %s'):format(playerName, source, tostring(isVip)))

    TriggerClientEvent('chat:addMessage', source, {
        color = isVip and {0, 255, 0} or {255, 0, 0},
        args = {'[VIP TEST]', ('Your VIP status: %s'):format(isVip and 'TRUE (VIP)' or 'FALSE (Not VIP)')}
    })

    -- Also test the main resource directly
    local mainSuccess, mainResult = pcall(function()
        return exports['koth_teamsel']:IsPlayerVIP(source)
    end)

    if mainSuccess then
        TriggerClientEvent('chat:addMessage', source, {
            color = {0, 255, 255},
            args = {'[MAIN RESOURCE]', ('Main resource VIP check: %s'):format(tostring(mainResult))}
        })
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            args = {'[MAIN RESOURCE]', 'Failed to check main resource VIP function'}
        })
    end
end, false)

print(('[%s] Server loaded successfully'):format(RESOURCE_NAME))
