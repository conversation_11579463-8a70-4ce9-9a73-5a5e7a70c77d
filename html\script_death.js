// =====================================================
// KOTH DEATH SCREEN - Clean Implementation
// =====================================================
console.log('[KOTH DEATH] Loading death screen script...');

// Death screen state
const DeathScreen = {
    isVisible: false,
    elements: {},
    
    // Initialize elements
    init() {
        this.elements = {
            deathScreen: document.getElementById('death-screen'),
            killerName: document.getElementById('killer-name'),
            killerId: document.getElementById('killer-id'),
            respawnFill: document.getElementById('respawn-fill'),
            bleedoutTimer: document.getElementById('bleedout-timer')
        };
        
        console.log('[KOTH DEATH] Elements initialized:', this.elements);
    },
    
    // Show death screen
    show(killerName, killerId) {
        if (!this.elements.deathScreen) {
            console.error('[KOTH DEATH] Death screen element not found!');
            return;
        }
        
        console.log('[KOTH DEATH] Showing death screen for killer:', killerName);
        
        // Show death screen with animation
        this.elements.deathScreen.style.display = 'flex';
        this.elements.deathScreen.style.visibility = 'visible';
        this.elements.deathScreen.style.opacity = '1';
        this.elements.deathScreen.style.animation = 'fadeIn 0.5s ease-in';
        
        // Update killer information
        if (this.elements.killerName) {
            this.elements.killerName.textContent = killerName || 'Unknown';
        }
        
        if (this.elements.killerId) {
            this.elements.killerId.textContent = killerId || '0';
        }
        
        // Reset respawn progress
        this.resetRespawnProgress();
        
        // Initialize bleedout timer
        this.updateBleedoutTimer(50);
        
        this.isVisible = true;
    },
    
    // Hide death screen
    hide() {
        if (!this.elements.deathScreen) return;
        
        console.log('[KOTH DEATH] Hiding death screen');
        
        // Add fade-out animation
        this.elements.deathScreen.style.animation = 'fadeOut 0.5s ease-out';
        
        setTimeout(() => {
            this.elements.deathScreen.style.display = 'none';
            this.elements.deathScreen.style.visibility = 'hidden';
            this.elements.deathScreen.style.opacity = '0';
        }, 500);
        
        this.isVisible = false;
    },
    
    // Update bleedout timer
    updateBleedoutTimer(seconds) {
        if (!this.elements.bleedoutTimer) return;
        
        this.elements.bleedoutTimer.textContent = seconds.toString();
        
        // Add urgency styling based on time remaining
        if (seconds <= 10) {
            this.elements.bleedoutTimer.style.color = '#ff0000';
            this.elements.bleedoutTimer.style.animation = 'pulse 1s infinite';
        } else if (seconds <= 20) {
            this.elements.bleedoutTimer.style.color = '#ff6600';
            this.elements.bleedoutTimer.style.animation = 'none';
        } else {
            this.elements.bleedoutTimer.style.color = '#ffffff';
            this.elements.bleedoutTimer.style.animation = 'none';
        }
    },
    
    // Update respawn progress bar
    updateRespawnProgress(progress) {
        if (!this.elements.respawnFill) return;
        
        this.elements.respawnFill.style.width = progress + '%';
        
        // Change color based on progress
        if (progress >= 75) {
            this.elements.respawnFill.style.backgroundColor = '#00ff00';
        } else if (progress >= 50) {
            this.elements.respawnFill.style.backgroundColor = '#ffff00';
        } else {
            this.elements.respawnFill.style.backgroundColor = '#ff0000';
        }
    },
    
    // Reset respawn progress
    resetRespawnProgress() {
        if (this.elements.respawnFill) {
            this.elements.respawnFill.style.width = '0%';
            this.elements.respawnFill.style.backgroundColor = '#ff0000';
        }
    }
};

// =====================================================
// MESSAGE HANDLER
// =====================================================
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch (data.action) {
        case 'showDeathScreen':
            console.log('[KOTH DEATH] Received showDeathScreen message:', data);
            DeathScreen.show(data.killer, data.killerId);
            break;
            
        case 'hideDeathScreen':
            console.log('[KOTH DEATH] Received hideDeathScreen message');
            DeathScreen.hide();
            break;
            
        case 'updateDeathTimer':
            if (DeathScreen.isVisible) {
                DeathScreen.updateBleedoutTimer(data.bleedoutTimer || 0);
            }
            break;
            
        case 'updateRespawnProgress':
            if (DeathScreen.isVisible) {
                DeathScreen.updateRespawnProgress(data.progress || 0);
            }
            break;
            
        default:
            // Ignore other messages
            break;
    }
});

// =====================================================
// CSS ANIMATIONS
// =====================================================
const deathScreenStyles = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(20px); }
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    
    #death-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: 'Segoe UI', 'Arial', sans-serif;
    }
    
    .death-content {
        text-align: center;
        color: white;
        max-width: 500px;
        width: 90%;
        padding: 40px;
        background: rgba(0, 0, 0, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
    }
    
    .death-title {
        font-size: 32px;
        font-weight: 300;
        color: #ffffff;
        margin-bottom: 30px;
        text-transform: uppercase;
        letter-spacing: 2px;
    }
    
    .respawn-section {
        margin: 30px 0;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }
    
    .respawn-instruction {
        font-size: 18px;
        margin-bottom: 15px;
        color: #ffffff;
        font-weight: 400;
    }
    
    .key-highlight {
        color: #ffffff;
        font-weight: 600;
        padding: 4px 8px;
        border: 1px solid #ffffff;
        border-radius: 3px;
        background: rgba(255, 255, 255, 0.1);
        display: inline-block;
        margin: 0 4px;
        font-size: 16px;
    }
    
    .respawn-progress {
        margin: 20px 0;
    }
    
    .respawn-bar {
        width: 300px;
        max-width: 100%;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        margin: 0 auto;
        overflow: hidden;
        position: relative;
    }
    
    .respawn-fill {
        height: 100%;
        background: #ffffff;
        transition: width 0.1s ease-out;
        width: 0%;
    }
    
    .bleedout-section {
        margin: 30px 0;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }
    
    .bleedout-text {
        font-size: 16px;
        color: #ffffff;
        margin-bottom: 10px;
        font-weight: 400;
    }
    
    #bleedout-timer {
        font-weight: 600;
        font-size: 20px;
        color: #ffffff;
        display: inline-block;
        margin: 0 4px;
    }
    
    .medic-call {
        font-size: 14px;
        margin-top: 15px;
        color: rgba(255, 255, 255, 0.7);
        font-style: italic;
        font-weight: 300;
    }
    
    .medic-highlight {
        color: #ffffff;
        font-weight: 400;
    }
    
    .killer-info {
        margin-top: 30px;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }
    
    .killer-text {
        font-size: 16px;
        color: #ffffff;
        font-weight: 400;
    }
    
    .killer-id {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 400;
    }
    
    .killer-name {
        color: #ffffff;
        font-weight: 500;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .death-content {
            padding: 30px 20px;
            margin: 20px;
        }
        
        .death-title {
            font-size: 28px;
        }
        
        .respawn-instruction {
            font-size: 16px;
        }
        
        .respawn-bar {
            width: 100%;
        }
        
        .key-highlight {
            font-size: 14px;
            padding: 3px 6px;
        }
    }
`;

// =====================================================
// INITIALIZATION
// =====================================================
document.addEventListener('DOMContentLoaded', function() {
    console.log('[KOTH DEATH] DOM loaded, initializing death screen...');
    
    // Add CSS styles
    const styleSheet = document.createElement('style');
    styleSheet.textContent = deathScreenStyles;
    document.head.appendChild(styleSheet);
    
    // Initialize death screen
    DeathScreen.init();
    
    console.log('[KOTH DEATH] Death screen script loaded successfully');
});

// Fallback initialization if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', DeathScreen.init.bind(DeathScreen));
} else {
    DeathScreen.init();
}

console.log('[KOTH DEATH] Death screen script ready');
