# Weapon Rental System

## Overview
The weapon rental system has been added to allow players to rent weapons at 30% of the purchase price. This provides a more affordable option for players who want to try different weapons without committing to a full purchase.

## Features

### 1. Rental Pricing
- All weapons can be rented at 30% of their purchase price
- Example: A $1000 weapon can be rented for $300

### 2. UI Updates
- Each weapon card now shows both BUY and RENT buttons
- Rent price is displayed below the purchase price
- Buttons are disabled if player can't afford them
- Blue color scheme for rent buttons to differentiate from green buy buttons

### 3. Auto-Close Feature
- The weapon shop UI now automatically closes after purchasing or renting a weapon
- This provides a smoother user experience and prevents accidental multiple purchases

## How It Works

### Client Side
1. When opening weapon shop, rent prices are calculated at 30% of buy price
2. Both BUY and RENT buttons are displayed for each weapon
3. Clicking either button sends the purchase type to the server
4. UI closes immediately after selection

### Server Side
1. Server receives the purchase type ('buy' or 'rent')
2. Deducts the appropriate amount from player's money
3. Gives the weapon to the player
4. Shows appropriate notification (Purchased vs Rented)
5. Sends close UI event to client

## Code Changes

### HTML/JavaScript (script.js)
- Added rent price calculation
- Added rent button to weapon cards
- Modified click handler to include purchase type
- Added auto-close functionality

### CSS (style.css)
- Added styling for weapon rent buttons
- Blue color scheme for rent buttons
- Proper button layout with flexbox

### Server (server.lua)
- Modified `koth:selectLoadout` to accept purchase type
- Added rental logic
- Added UI close event after successful purchase

### Client (client.lua)
- Updated weapon selection callback to include purchase type
- Added handler for weapon shop close event

## Testing

To test the weapon rental system:

1. Open the class menu by pressing E on the marine NPC
2. Select any unlocked class
3. You'll see weapons with both BUY and RENT options
4. Rent prices are 30% of buy prices
5. Click RENT to rent a weapon
6. The UI will close automatically after renting

## Future Enhancements

Possible future additions:
- Rental duration system (weapons expire after time)
- Different rental percentages for different weapon tiers
- Rental history tracking
- Option to upgrade rental to purchase
