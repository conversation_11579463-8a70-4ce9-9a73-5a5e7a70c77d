@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: transparent;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

#container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 50px;
}

.shop-panel {
    width: 400px;
    height: 600px;
    background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
    border-radius: 15px;
    border: 2px solid #4a90e2;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    position: relative;
}

.header {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    position: relative;
}

.header-icon {
    font-size: 24px;
    margin-right: 15px;
    color: white;
}

.header-text h2 {
    color: white;
    font-size: 16px;
    font-weight: 700;
    margin: 0;
    text-transform: uppercase;
}

.header-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin: 0;
    text-transform: uppercase;
}

.close-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.category-tabs {
    display: flex;
    background: #1a2332;
    border-bottom: 1px solid #4a90e2;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    text-transform: uppercase;
    font-weight: 500;
}

.tab-btn.active {
    color: #4a90e2;
    background: rgba(74, 144, 226, 0.1);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.tab-icon {
    font-size: 16px;
}

.search-container {
    padding: 15px 20px;
    display: flex;
    gap: 10px;
}

.search-input {
    flex: 1;
    background: #2d3748;
    border: 1px solid #4a90e2;
    border-radius: 8px;
    padding: 10px 15px;
    color: white;
    font-size: 12px;
    text-transform: uppercase;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-btn {
    background: #4a90e2;
    border: none;
    border-radius: 8px;
    padding: 10px 15px;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #357abd;
}

.weapon-category {
    padding: 10px 20px;
    background: #1a2332;
    border-bottom: 1px solid #4a90e2;
}

.weapon-category h3 {
    color: white;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
}

.skin-items {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.skin-item {
    background: #2d3748;
    border: 1px solid #4a90e2;
    border-radius: 10px;
    margin-bottom: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s;
}

.skin-item:hover {
    background: #3a4a5c;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.skin-image {
    width: 80px;
    height: 50px;
    background: #1a2332;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.skin-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.skin-info {
    flex: 1;
}

.skin-info h4 {
    color: white;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.skin-price {
    color: #ffd700;
    font-size: 11px;
    font-weight: 500;
    margin-bottom: 2px;
}

.skin-rarity {
    color: #4a90e2;
    font-size: 10px;
    margin-bottom: 2px;
}

.skin-quantity {
    color: rgba(255, 255, 255, 0.7);
    font-size: 10px;
}

.buy-btn {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    color: white;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.3s;
}

.buy-btn:hover {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
    transform: scale(1.05);
}

/* Scrollbar styling */
.skin-items::-webkit-scrollbar {
    width: 6px;
}

.skin-items::-webkit-scrollbar-track {
    background: #1a2332;
}

.skin-items::-webkit-scrollbar-thumb {
    background: #4a90e2;
    border-radius: 3px;
}

.skin-items::-webkit-scrollbar-thumb:hover {
    background: #357abd;
}
