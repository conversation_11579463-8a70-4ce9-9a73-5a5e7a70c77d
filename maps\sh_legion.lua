MAPS = MAPS or {}
MAPS['legion'] = {
    friendlyName = "Legion Square",
    Spawns = {
        red = { coords = vector3(696.15, 634.14, 128.91 ), radius = 200.0 },
        green = { coords = vector3(1068.74, -2181.75, 31.54), radius = 200.0 },
        blue = { coords = vector3(-1598.73, -920.55, 8.98), radius = 200.0 }
    },
    Hill = { coords = vector3(181.37, -968.95, 29.58), radius = 225.0 },
    keyPoints = {
        vector3(148.44, -1040.19, 29.89),
        vector3(23.91, -1074.49, 43.89),
        vector3(74.0, -876.53, 31.87),
        vector3(42.21, -1005.48, 35.13),
        vector3(289.33, -999.11, 53.71)
    },
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(684.74, 624.7, 128.91), heading = 309.97,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(696.54, 620.61, 128.91), heading = 14.85, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(620.72, 607.111, 128.911), heading = 335.526, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(598.028, 615.424, 128.911), heading = 335.526, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(703.47, 631.85, 128.89), heading = 68.03, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(699.68, 645.28, 129.1), heading = 158.74, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(696.45, 618.58, 128.75), heading = 294.4 },
        Spawnpoints = {
            Cars = { coords = vector3(688.2604, 643.931, 129.6814), heading = 247.59 },
            Helicopters = {coords = vector3(652.6, 622.61, 129.37), heading = 339.33 },
        },
        RespawnVehicle = {coords = vector3(710.73,622.932,128.896), heading = 249.449},
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(1084.138, -2164.838, 31.40108), heading = 125.99,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(1070.35, -2159.29, 32.39), heading = 143.981, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(1073.95, -2188.36, 31.26), heading = 19.79, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(1062.264, -2186.18, 31.333), heading = 84.083, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(1083.63, -2178.474609375, 31.38), heading = 70.86, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(1065.77, -2170.96, 31.98), heading = 218.27, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(1073.18, -2153.94, 32.36), heading = 45.639 },
        Spawnpoints = {
            Cars = {coords = vector3(1060.29, -2170.43, 32.61), heading = 353.71 },
            Helicopters = {coords = vector3(1047.54, -2280.39, 30.56), heading = 355.048 },
        },
        RespawnVehicle = {coords = vector3(1050.739,-2168.651,31.807), heading = 357.165},
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(-1606.567, -928.0815, 8.986135), heading = 5.0, model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(-1599.58, -910.84, 9.1), heading = 95.05, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(-1624.49, -890.474, 9.092), heading = 138.0, model = 'csb_mweather'},
            { type = 'Repair', coords = vector3(-1640.705, -876.28, 9.096), heading = 137.835, model = 's_m_y_armymech_01'},
            { type = 'Attachments', coords = vector3(-1624.25, -919.60, 8.70), heading = 291.96, model = 'gr_prop_gr_bench_03a'},
            { type = 'Cosmic', coords = vector3(-1592.61, -925.93, 8.98), heading = 59.53, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(-1597.55, -911.7, 8.96), heading = 3.34 },
        Spawnpoints = {
            Cars = {coords = vector3(-1591.88, -890.04, 10.32), heading = 318.57 },
            Helicopters = {coords = vector3(-1633.27, -903.85, 9.28), heading = 229.73 },
        },
        RespawnVehicle = {coords = vector3(-1612.233,-912.633,8.925), heading = 323.15},
    },
}