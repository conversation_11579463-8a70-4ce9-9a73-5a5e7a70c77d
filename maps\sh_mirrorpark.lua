MAPS = MAPS or {}
MAPS['mirrorpark'] = {
    friendlyName = "Mirror Park",
    Spawns = {
        red = { coords = vector3(1063.81, -2173.43, 31.87 ), radius = 200.0 },
        green={ coords = vector3(-464.13, -625.25, 31.17), radius = 200.0 },
        blue ={ coords = vector3(696.15, 634.14, 128.91), radius = 200.0 }
    },
    Hill = { coords = vector3(1114.99, -549.07, 58.09), radius = 245.0 },
    keyPoints = {
        vector3(1151.18, -434.78, 80.86),
        vector3(1153.74, -344.25, 78.62),
        vector3(1016.36, -492.95, 67.18),
        vector3(1222.92, -596.29, 72.74),
        vector3(1144.43, -778.36, 59.61),
        vector3(1084.25, -696.04, 58.83),
        vector3(1118.6, -647.41, 61.8)
    },
    blue = {
        Shops = {
            { type = 'Weapons', coords = vector3(684.74, 624.7, 128.91), heading = 309.97,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(696.54, 620.61, 128.91), heading = 14.85, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(620.72, 607.111, 128.911), heading = 335.526, model = 'csb_mweather'},
            { type = 'Attachments', coords = vector3(703.47, 631.85, 128.89), heading = 68.03, model = 'gr_prop_gr_bench_03a'},
            { type = 'Repair', coords = vector3(598.028, 615.424, 128.911), heading = 335.526, model = 's_m_y_armymech_01'},
            { type = 'Cosmic', coords = vector3(699.68, 645.28, 129.1), heading = 158.74, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(696.45, 618.58, 128.75), heading = 294.4 },
        Spawnpoints = {
            Cars = { coords = vector3(688.2604, 643.931, 129.6814), heading = 247.59 },
            Helicopters = {coords = vector3(652.6, 622.61, 129.37), heading = 339.33 },
        },
        RespawnVehicle = {coords = vector3(710.545,623.064,128.896), heading = 249.449},
    },
    green = {
        Shops = {
            { type = 'Weapons', coords = vector3(-457.21, -614.67, 31.17), heading = 142.31,model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(-468.17, -615.71, 31.17), heading = 221.51, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(-557.41, -670.96, 33.141), heading = 358.99, model = 'csb_mweather'},
            { type = 'Attachments', coords = vector3(-458.76, -625.50, 31.16), heading = 0.0, model = 'gr_prop_gr_bench_03a'},
            { type = 'Repair', coords = vector3(-549.81, -719.4, 33.159), heading = 278.178, model = 's_m_y_armymech_01'},
            { type = 'Cosmic', coords = vector3(-471.14, -626.4, 31.17), heading = 314.65, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(-469.53, -614.1, 31.02), heading = 122.76 },
        Spawnpoints = {
            Cars = { coords = vector3(-481.85, -618.12, 32.27), heading = 177.36 },
            Helicopters = {coords = vector3(-515.09, -657.93, 33.34), heading = 264.16 },
        },
        RespawnVehicle = {coords = vector3(-481.978,-604.47,31.167), heading = 181.417},
    },
    red = {
        Shops = {
            { type = 'Weapons', coords = vector3(1084.138, -2164.838, 31.40108), heading = 125.99, model = 's_m_y_marine_03'},
            { type = 'Vehicles', coords = vector3(1070.35, -2159.29, 32.39), heading = 143.981, model = 's_m_m_marine_01'},
            { type = 'Ammo', coords = vector3(1073.95, -2188.36, 31.26), heading = 19.79, model = 'csb_mweather'},
            { type = 'Attachments', coords = vector3(1083.63, -2178.474609375, 31.38), heading = 70.86, model = 'gr_prop_gr_bench_03a'},
            { type = 'Repair', coords = vector3(1062.264, -2186.18, 31.333), heading = 84.083, model = 's_m_y_armymech_01'},
            { type = 'Cosmic', coords = vector3(1064.85, -2163.64, 32.25), heading = 192.76, model = 'a_c_shepherd' },
        },
        CarModel = { coords = vector3(1073.18, -2153.94, 32.36), heading = 45.639 },
        Spawnpoints = {
            Cars = {coords = vector3(1060.29, -2170.43, 32.61), heading = 353.71 },
            Helicopters = {coords = vector3(1047.54, -2280.39, 30.56), heading = 355.048 },
        },
        RespawnVehicle = {coords = vector3(1050.527,-2170.338,31.74), heading = 357.165},
    }
}