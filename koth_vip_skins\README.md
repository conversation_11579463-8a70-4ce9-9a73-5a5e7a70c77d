# VIP Weapon Skin Changer

A premium weapon skin changer system for VIP players in the KOTH gamemode. Features a modern UI with drag-and-drop functionality, live 3D weapon preview, and persistent skin storage.

## Features

### 🎯 Core Features
- **VIP-Only Access**: Only players with VIP status can use the skin changer
- **Live 3D Preview**: See weapon skins applied in real-time on a rotating 3D model
- **Drag & Drop Interface**: Intuitive drag-and-drop skin selection
- **Persistent Storage**: Skin preferences are saved and auto-applied
- **Modern UI**: Sleek, animated interface with visual effects

### 🎨 Skin System
- **8 Weapon Tints**: Default, Green, Gold, Pink, Army, LSPD, Orange, Platinum
- **Live Preview**: See skins applied instantly on your weapon
- **Auto-Application**: Saved skins automatically apply when switching weapons
- **Per-Weapon Storage**: Different skins for different weapons

### 🔊 Audio & Visual Effects
- **Sound Effects**: UI interactions with audio feedback
- **Smooth Animations**: Polished transitions and effects
- **Visual Feedback**: Hover effects, selection highlights, drop animations

## Installation

### 1. Database Setup
Run the SQL script to create the required table:
```sql
-- Execute this in your MySQL database
source koth_vip_skins/sql_create_vip_skins_table.sql
```

### 2. Resource Installation
1. Place the `koth_vip_skins` folder in your resources directory
2. Add to your `server.cfg`:
```
ensure koth_vip_skins
```

### 3. Dependencies
- **koth_teamsel**: Main KOTH resource (for VIP checking)
- **oxmysql**: MySQL connector
- **badger_discord_roles**: Discord role checking (optional)

## Usage

### For Players
1. Hold any weapon in your hands
2. Type `/vipskin` in chat
3. Use the interface to select and preview skins
4. Drag skins to the preview area or click to select
5. Click "Apply Skin" to save your choice

### For Admins
- The system automatically integrates with your existing VIP system
- No additional configuration required
- Skin preferences are stored per-player in the database

## Commands

| Command | Description | Access |
|---------|-------------|---------|
| `/vipskin` | Open the VIP skin changer | VIP Players Only |

## Configuration

### VIP Integration
The system automatically detects VIP status using:
1. Main KOTH resource's `IsPlayerVIP` function
2. Fallback to Discord role checking
3. Same VIP roles as configured in main resource

### Customization
- **Weapon Tints**: Modify `weaponTints` in `client.lua`
- **UI Colors**: Edit CSS variables in `style.css`
- **Sound Effects**: Add OGG files to `html/sounds/`

## File Structure
```
koth_vip_skins/
├── fxmanifest.lua              # Resource manifest
├── server.lua                  # Server-side logic
├── client.lua                  # Client-side logic
├── sql_create_vip_skins_table.sql  # Database schema
├── html/
│   ├── index.html              # UI structure
│   ├── style.css               # UI styling
│   ├── script.js               # UI interactions
│   ├── sounds/                 # Sound effects (optional)
│   └── images/                 # UI images (optional)
└── README.md                   # This file
```

## Technical Details

### Database Schema
```sql
CREATE TABLE koth_vip_skins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    txid VARCHAR(50) NOT NULL,
    weapon_hash VARCHAR(20) NOT NULL,
    tint_id INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_player_weapon (txid, weapon_hash)
);
```

### Events
- `koth_vip_skins:open_skin_changer` - Opens the UI
- `koth_vip_skins:apply_skin` - Applies a weapon skin
- `koth_vip_skins:load_preferences` - Loads saved skins

### Security
- VIP status verified on every command use
- Server-side validation of all skin applications
- SQL injection protection with parameterized queries

## Troubleshooting

### Common Issues

**"This command is only available to VIP players!"**
- Ensure the player has VIP status in the main KOTH resource
- Check Discord role configuration
- Verify badger_discord_roles is working

**UI doesn't open**
- Check browser console for JavaScript errors
- Ensure all HTML/CSS/JS files are present
- Verify resource is started after dependencies

**Skins don't save**
- Check MySQL connection
- Verify database table exists
- Check server console for SQL errors

**3D preview not working**
- This is normal - GTA V has limitations with weapon object previews
- The live weapon preview on the player still works
- Consider this a visual enhancement feature

## Support

For issues or questions:
1. Check the server console for error messages
2. Verify all dependencies are installed and working
3. Test with a known VIP player account
4. Check database connectivity and table structure

## Credits

Created for the KOTH gamemode with love ❤️
- Modern UI design with CSS3 animations
- Drag & drop functionality
- Integration with existing VIP systems
- Persistent data storage
