# UI Visibility Fix for KOTH System

## Problem
When pressing E on the ped, the UI appears invisible and the cursor gets stuck on screen, preventing character movement.

## Solution Applied

### 1. Created Consolidated UI Handler (`ui_fix.js`)
- Single script to handle all UI interactions
- Proper show/hide functionality
- Ensures elements are visible with proper CSS properties
- Handles NUI focus correctly

### 2. Simplified HTML Structure (`ui_simple.html`)
- Removed conflicting script files
- Only loads essential scripts: `ui_fix.js` and `script_death.js`
- Maintains all UI elements but with cleaner script loading

### 3. Updated fxmanifest.lua
- Changed ui_page to use `ui_simple.html`
- Added new files to the files list

## Testing Steps

1. **Restart the resource**:
   ```
   ensure koth_teamsel
   ```

2. **Test Class Selection**:
   - Approach the Classes ped
   - Press E
   - The class selection UI should appear with:
     - Dark background overlay
     - Class cards visible
     - Proper mouse cursor
     - Close button (X) functional

3. **Test Vehicle Shop**:
   - Approach the Vehicle ped
   - Press E
   - The vehicle shop should display with:
     - Vehicle grid
     - Money display
     - Buy/Rent buttons

4. **Test Weapon Shop**:
   - Select a class first
   - The weapon shop should open with:
     - Category tabs
     - Weapon grid
     - Price display

## If UI is Still Invisible

1. **Check Browser Console** (F8 in game):
   - Look for JavaScript errors
   - Check if NUI messages are being received

2. **Verify CSS is Loading**:
   - The style.css file should be applying styles
   - Elements should have proper opacity and visibility

3. **Test with Debug Commands**:
   - `/showteamselect` - Force show team selection
   - `/testkillreward` - Test kill reward popup
   - `/testkillpopup zone` - Test zone kill popup

## Common Issues and Fixes

### Issue: UI appears but no cursor
**Fix**: The `SetNuiFocus(true, true)` call might be failing. Check client.lua for proper NUI focus calls.

### Issue: UI is transparent/invisible
**Fix**: Check that CSS opacity is set correctly. The ui_fix.js ensures opacity: 1 on show.

### Issue: Can't close UI
**Fix**: ESC key and close buttons are handled in ui_fix.js. Make sure NUI callbacks are working.

### Issue: Multiple UIs showing at once
**Fix**: The hideAllUI() function in ui_fix.js ensures only one UI shows at a time.

## File Structure
```
koth_teamsel/
├── fxmanifest.lua (updated to use ui_simple.html)
├── client.lua (handles NUI triggers)
├── server.lua (handles data and rewards)
└── html/
    ├── ui_simple.html (simplified HTML without script conflicts)
    ├── ui_fix.js (consolidated UI handler)
    ├── style.css (all UI styles)
    └── script_death.js (death system handler)
```

## Key Changes Made

1. **Removed Script Conflicts**: The original ui.html was loading 7+ JavaScript files that were conflicting
2. **Centralized UI Management**: All UI show/hide logic is now in ui_fix.js
3. **Proper Element Visibility**: Ensures elements have correct CSS properties when shown
4. **Clean Event Handling**: Single message handler for all NUI events

## Next Steps

1. Test the UI with the simplified setup
2. Add placeholder images to `html/images/classes/` and `html/images/guns/` folders
3. Verify database integration is working for money/XP updates
4. Test the kill reward system shows properly

The UI should now be visible and functional when you press E on the peds!
