-- Engineer class ability: Repair Tool
-- This ability allows the engineer to repair nearby vehicles and helicopters.  When the
-- player activates the ability via the hotbar (slot 5), they will hold a wrench
-- prop.  While holding the tool, the player can approach a vehicle or helicopter
-- and press the E key to begin repairing.  The repair takes a fixed duration to
-- complete and fully repairs the target.  After use, there is a cooldown before
-- the ability can be used again.

local isRepairing = false
local repairProp = nil
local lastRepairTime = 0

-- Debug print helper
local function debugPrint(...)
    if Config.Debug then
        print('[KOTH Classes - Engineer]', ...)
    end
end

-- Clean up function to detach and delete the prop
local function removeRepairProp()
    if repairProp and DoesEntityExist(repairProp) then
        DeleteEntity(repairProp)
    end
    repairProp = nil
end

-- Ability event handler
RegisterNetEvent('koth_classes:useEngineerAbility')
AddEventHandler('koth_classes:useEngineerAbility', function(ability)
    debugPrint('Engineer ability activated')

    if isRepairing then
        debugPrint('Already repairing, ignoring new request')
        return
    end

    -- Check cooldown
    local currentTime = GetGameTimer()
    local cooldownMs = (ability.cooldown or 120) * 1000
    if currentTime < lastRepairTime + cooldownMs then
        local remaining = math.ceil(((lastRepairTime + cooldownMs) - currentTime) / 1000)
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName(string.format("Repair Tool on cooldown: %d seconds", remaining))
        EndTextCommandThefeedPostTicker(false, true)
        return
    end

    -- Begin repair mode: spawn prop and wait for player to press E near a vehicle
    isRepairing = true

    local playerPed = PlayerPedId()
    local modelHash = GetHashKey(ability.prop or 'prop_cs_wrench')
    RequestModel(modelHash)
    while not HasModelLoaded(modelHash) do
        Citizen.Wait(0)
    end

    -- Create the prop and attach to player's right hand (bone index 57005 or 28422)
    local prop = CreateObject(modelHash, 0.0, 0.0, 0.0, true, true, false)
    local boneIndex = GetPedBoneIndex(playerPed, 28422)
    AttachEntityToEntity(prop, playerPed, boneIndex, 0.1, 0.0, -0.02, 0.0, 90.0, 0.0, true, true, false, true, 1, true)
    repairProp = prop

    -- Show instructions to player
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("Approach a vehicle and press E to repair (ESC to cancel)")
    EndTextCommandThefeedPostTicker(false, true)

    -- Start a thread to listen for the E key
    Citizen.CreateThread(function()
        local cancelled = false
        while isRepairing and not cancelled do
            Citizen.Wait(0)
            -- Cancel with ESC/BACKSPACE (key 177)
            if IsControlJustPressed(0, 177) then
                cancelled = true
                break
            end
            -- Repair when E (key 38) is pressed
            if IsControlJustPressed(0, 38) then
                local coords = GetEntityCoords(playerPed)
                -- Find the closest vehicle within repairRange
                local repairRange = ability.repairRange or 3.0
                local vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, repairRange, 0, 71)
                if vehicle and DoesEntityExist(vehicle) then
                    debugPrint('Repairing vehicle', vehicle)
                    -- Start repair: freeze player and play welding animation
                    FreezeEntityPosition(playerPed, true)
                    TaskStartScenarioInPlace(playerPed, 'WORLD_HUMAN_WELDING', 0, true)
                    -- Wait for duration
                    local repairDuration = (ability.duration or 10) * 1000
                    local startTime = GetGameTimer()
                    while GetGameTimer() - startTime < repairDuration do
                        Citizen.Wait(0)
                    end
                    -- Complete repair: fix vehicle
                    SetVehicleFixed(vehicle)
                    SetVehicleEngineHealth(vehicle, 1000.0)
                    SetVehicleBodyHealth(vehicle, 1000.0)
                    -- End animation and unfreeze
                    ClearPedTasks(playerPed)
                    FreezeEntityPosition(playerPed, false)
                    -- Notify success
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Vehicle repaired!")
                    EndTextCommandThefeedPostTicker(false, true)
                    -- Update cooldown
                    lastRepairTime = GetGameTimer()
                    -- Set ability cooldown via export so hotbar displays remaining time
                    if exports['koth_classes'] then
                        exports['koth_classes']:SetAbilityCooldown(ability.slot, ability.cooldown)
                    end
                    break
                else
                    -- No vehicle nearby
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("No vehicle nearby to repair")
                    EndTextCommandThefeedPostTicker(false, true)
                end
            end
        end
        -- Repair loop finished or cancelled
        isRepairing = false
        -- Clean up prop
        removeRepairProp()
        if cancelled then
            -- Cancel message
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName("Repair cancelled")
            EndTextCommandThefeedPostTicker(false, true)
        end
    end)
end)